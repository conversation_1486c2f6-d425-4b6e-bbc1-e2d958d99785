// 智能网页总结助手 - 侧边栏脚本

class SidebarApp {
  constructor() {
    this.currentPageInfo = null;
    this.isLoading = false;
    this.config = null;
    this.progressInterval = null;
    this.toastTimeout = null;
    this.currentMarkdown = '';
    this.markdownParser = new MarkdownParser();
    this.currentOperation = null; // 当前操作的控制器
    this.dingTalkAuthStatus = null; // 钉钉认证状态

    // 初始化UI权限控制管理器
    this.permissionManager = new UIPermissionManager();

    this.initializeElements();
    this.bindEvents();
    this.initializeKeyboardShortcuts();
    this.initializeVisibilityDetection();
    this.loadConfig();
    this.updatePageInfo();
    this.initializeDingTalkAuth(); // 初始化钉钉认证

    // 设置权限变化监听器
    this.setupPermissionListener();

    // 显示欢迎提示
    setTimeout(() => {
      this.showToast('欢迎使用智能网页总结助手！按 Ctrl+Enter 快速开始总结', 'info', 5000);
    }, 1000);
  }

  // 初始化DOM元素引用
  initializeElements() {
    // 钉钉认证相关元素
    this.dingTalkAuthStatus = document.getElementById('dingTalkAuthStatus');
    this.authNotLoggedIn = document.getElementById('authNotLoggedIn');
    this.authLoggedIn = document.getElementById('authLoggedIn');
    this.authLoginBtn = document.getElementById('authLoginBtn');
    this.authHelpBtn = document.getElementById('authHelpBtn');
    // 移除了authName、authOrg、authAvatar、authRefreshBtn的引用，因为新的简洁界面不需要这些元素
    this.authSettingsBtn = document.getElementById('authSettingsBtn');
    this.authLogoutBtn = document.getElementById('authLogoutBtn');

    // 按钮元素
    this.summarizeBtn = document.getElementById('summarizeBtn');
    this.markdownBtn = document.getElementById('markdownBtn');
    this.settingsBtn = document.getElementById('settingsBtn');
    this.themeToggle = document.getElementById('themeToggle');
    this.popupBtn = document.getElementById('popupBtn');
    this.copyBtn = document.getElementById('copyBtn');
    this.exportBtn = document.getElementById('exportBtn');
    this.retryBtn = document.getElementById('retryBtn');

    // Markdown相关按钮
    this.markdownPreviewBtn = document.getElementById('markdownPreviewBtn');
    this.markdownCopyBtn = document.getElementById('markdownCopyBtn');
    this.markdownDownloadBtn = document.getElementById('markdownDownloadBtn');
    this.markdownEditBtn = document.getElementById('markdownEditBtn');
    this.previewTab = document.getElementById('previewTab');
    this.sourceTab = document.getElementById('sourceTab');
    
    // 显示区域
    this.pageInfo = document.getElementById('pageInfo');
    this.pageTitle = document.getElementById('pageTitle');
    this.pageUrl = document.getElementById('pageUrl');
    this.pageStats = document.getElementById('pageStats');
    this.templateSelect = document.getElementById('templateSelect');
    this.templateSection = document.getElementById('templateSection');
    this.featureContainer = document.getElementById('featureContainer');
    this.authRequiredOverlay = document.getElementById('authRequiredOverlay');
    
    // 状态区域
    this.resultSection = document.getElementById('resultSection');
    this.resultContent = document.getElementById('resultContent');
    this.resultMeta = document.getElementById('resultMeta');
    this.loadingSection = document.getElementById('loadingSection');
    this.errorSection = document.getElementById('errorSection');
    this.errorMessage = document.getElementById('errorMessage');

    // Markdown区域
    this.markdownSection = document.getElementById('markdownSection');
    this.markdownPreview = document.getElementById('markdownPreview');
    this.markdownSource = document.getElementById('markdownSource');
    this.markdownTextarea = document.getElementById('markdownTextarea');
    this.markdownMeta = document.getElementById('markdownMeta');
    
    // 进度条
    this.progressFill = document.getElementById('progressFill');
    this.progressText = document.getElementById('progressText');
    
    // Toast
    this.toast = document.getElementById('toast');
    this.toastMessage = document.getElementById('toastMessage');

    // 滚动提示
    this.scrollHint = document.getElementById('scrollHint');
  }

  // 绑定事件监听器
  bindEvents() {
    // 钉钉认证相关事件
    if (this.authLoginBtn) {
      this.authLoginBtn.addEventListener('click', () => this.handleDingTalkLogin());
    }
    if (this.authHelpBtn) {
      this.authHelpBtn.addEventListener('click', () => this.showDingTalkLoginHelp());
    }
    // 移除了刷新按钮的事件绑定，因为新的简洁界面不包含刷新按钮
    if (this.authSettingsBtn) {
      this.authSettingsBtn.addEventListener('click', () => this.openDingTalkSettings());
    }
    if (this.authLogoutBtn) {
      this.authLogoutBtn.addEventListener('click', () => this.handleDingTalkLogout());
    }

    // 调试按钮事件
    const authDebugBtn = document.getElementById('authDebugBtn');
    if (authDebugBtn) {
      authDebugBtn.addEventListener('click', () => this.showDingTalkDebugInfo());
    }

    this.summarizeBtn.addEventListener('click', () => this.handleSummarize());
    this.markdownBtn.addEventListener('click', () => this.handleMarkdownExtraction());
    this.settingsBtn.addEventListener('click', () => this.openSettings());
    this.themeToggle.addEventListener('click', () => this.toggleTheme());
    this.popupBtn.addEventListener('click', () => this.openSummaryPopup());
    this.copyBtn.addEventListener('click', () => this.copyResult());
    this.exportBtn.addEventListener('click', () => this.exportResult());
    this.retryBtn.addEventListener('click', () => this.handleSummarize());
    this.templateSelect.addEventListener('change', () => this.saveSelectedTemplate());

    // Markdown相关事件
    this.markdownPreviewBtn.addEventListener('click', () => this.openMarkdownPreview());
    this.markdownCopyBtn.addEventListener('click', () => this.copyMarkdown());
    this.markdownDownloadBtn.addEventListener('click', () => this.downloadMarkdown());
    this.markdownEditBtn.addEventListener('click', () => this.toggleMarkdownEdit());
    this.previewTab.addEventListener('click', () => this.showMarkdownPreview());
    this.sourceTab.addEventListener('click', () => this.showMarkdownSource());
    this.markdownTextarea.addEventListener('input', () => this.updateMarkdownPreview());
  }

  // 加载配置
  async loadConfig() {
    try {
      const response = await this.sendMessage({ action: 'getConfig' });
      if (response.success) {
        this.config = response.config;
        this.updateTemplateSelect();
        this.applyTheme();
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  }

  // 更新页面信息
  async updatePageInfo() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab) {
        this.pageTitle.textContent = tab.title || '未知页面';
        this.pageUrl.textContent = tab.url || '';
        this.pageUrl.title = tab.url || '';
        
        // 获取页面内容信息
        const response = await this.sendMessage({ action: 'extractContent' });
        if (response.success) {
          this.currentPageInfo = response;
          this.updatePageStats(response.content.wordCount);
        }
      }
    } catch (error) {
      console.error('更新页面信息失败:', error);
      this.pageTitle.textContent = '无法获取页面信息';
      this.pageUrl.textContent = '请刷新页面后重试';
    }
  }

  // 更新页面统计信息
  updatePageStats(wordCount) {
    const wordCountElement = this.pageStats.querySelector('.word-count');
    if (wordCountElement) {
      wordCountElement.textContent = `字数: ${wordCount || 0}`;
    }
  }

  // 更新模板选择器
  updateTemplateSelect() {
    if (!this.config || !this.config.promptTemplates) return;
    
    // 清空现有选项
    this.templateSelect.innerHTML = '';
    
    // 添加模板选项
    Object.entries(this.config.promptTemplates).forEach(([key, template]) => {
      const option = document.createElement('option');
      option.value = key;
      option.textContent = template.name;
      this.templateSelect.appendChild(option);
    });
    
    // 设置选中的模板
    if (this.config.selectedTemplate) {
      this.templateSelect.value = this.config.selectedTemplate;
    }
  }

  // 处理总结请求
  async handleSummarize() {
    if (this.isLoading) return;

    try {
      // 检查是否有页面内容
      if (!this.currentPageInfo) {
        await this.updatePageInfo();
      }

      if (!this.currentPageInfo || !this.currentPageInfo.content) {
        this.showError('无法获取页面内容，请刷新页面后重试');
        return;
      }

      // 检查API配置
      if (!this.config || !this.config.apiConfig || !this.config.apiConfig.apiKey) {
        this.showError('请先在设置中配置API密钥');
        return;
      }

      this.startLoading();

      // 发送总结请求
      const response = await this.sendMessage({
        action: 'summarizeContent',
        content: this.currentPageInfo.content.content,
        url: this.currentPageInfo.url,
        title: this.currentPageInfo.title
      });

      if (response.success) {
        this.showResult(response.summary, response.template);
      } else {
        this.showError(response.error || '总结生成失败');
      }
    } catch (error) {
      console.error('总结失败:', error);
      this.showError('网络错误，请检查网络连接后重试');
    } finally {
      this.stopLoading();
    }
  }

  // 处理Markdown提取请求
  async handleMarkdownExtraction() {
    if (this.isLoading) return;

    try {
      // 检查是否有页面内容
      if (!this.currentPageInfo) {
        await this.updatePageInfo();
      }

      if (!this.currentPageInfo || !this.currentPageInfo.content) {
        this.showError('无法获取页面内容，请刷新页面后重试');
        return;
      }

      // 检查API配置
      if (!this.config || !this.config.apiConfig || !this.config.apiConfig.apiKey) {
        this.showError('请先在设置中配置API密钥');
        return;
      }

      // 检查内容长度并给出提示
      const contentLength = this.currentPageInfo.content.content.length;
      if (contentLength > 10000) {
        const confirmed = await this.showConfirmDialog(
          '长文章处理提示',
          `检测到较长的文章内容（约${Math.round(contentLength/1000)}k字符），AI处理可能需要较长时间。是否继续？`,
          '继续处理',
          '取消'
        );
        if (!confirmed) return;
      }

      this.startMarkdownLoading();

      // 创建操作控制器
      this.currentOperation = new AbortController();

      // 发送Markdown提取请求
      const response = await this.sendMessage({
        action: 'extractMarkdown',
        content: this.currentPageInfo.content.content,
        url: this.currentPageInfo.url,
        title: this.currentPageInfo.title
      });

      if (response.success) {
        this.showMarkdownResult(response.markdown, response.chunks);
        this.showToast('Markdown提取完成！', 'success');
      } else {
        this.showError(response.error || 'Markdown提取失败');
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        this.showToast('操作已取消', 'info');
      } else {
        console.error('Markdown提取失败:', error);
        this.showError('网络错误，请检查网络连接后重试');
      }
    } finally {
      this.stopMarkdownLoading();
      this.currentOperation = null;
    }
  }

  // 开始加载状态
  startLoading() {
    this.isLoading = true;
    this.summarizeBtn.disabled = true;
    this.markdownBtn.disabled = true;
    this.summarizeBtn.classList.add('loading');
    this.hideAllSections();
    this.loadingSection.style.display = 'flex';

    // 更新按钮文本
    const buttonText = this.summarizeBtn.querySelector('span');
    if (buttonText) {
      buttonText.textContent = '正在总结...';
    }

    // 智能进度条动画
    let progress = 0;
    let stage = 0;
    const stages = [
      { text: '正在提取页面内容...', maxProgress: 20 },
      { text: '正在分析内容结构...', maxProgress: 40 },
      { text: '正在调用AI服务...', maxProgress: 70 },
      { text: '正在生成总结...', maxProgress: 90 },
      { text: '正在格式化结果...', maxProgress: 100 }
    ];

    const progressInterval = setInterval(() => {
      const currentStage = stages[stage];
      const increment = Math.random() * 3 + 1;
      progress = Math.min(progress + increment, currentStage.maxProgress);

      this.progressFill.style.width = `${progress}%`;
      this.progressText.textContent = `${Math.round(progress)}%`;

      // 更新加载文本
      const loadingText = document.querySelector('.loading-text');
      if (loadingText) {
        loadingText.textContent = currentStage.text;
      }

      // 切换到下一阶段
      if (progress >= currentStage.maxProgress && stage < stages.length - 1) {
        stage++;
      }

      if (!this.isLoading) {
        clearInterval(progressInterval);
        this.progressFill.style.width = '100%';
        this.progressText.textContent = '100%';

        // 完成动画
        setTimeout(() => {
          if (loadingText) {
            loadingText.textContent = '总结完成！';
          }
        }, 200);
      }
    }, 300);

    this.progressInterval = progressInterval;
  }

  // 停止加载状态
  stopLoading() {
    this.isLoading = false;
    this.summarizeBtn.disabled = false;
    this.markdownBtn.disabled = false;
    this.summarizeBtn.classList.remove('loading');
    this.loadingSection.style.display = 'none';

    // 恢复按钮文本
    const buttonText = this.summarizeBtn.querySelector('span');
    if (buttonText) {
      buttonText.textContent = '总结当前页面';
    }

    // 清理进度条定时器
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }

    // 重置进度条
    this.progressFill.style.width = '0%';
    this.progressText.textContent = '0%';
  }

  // 开始Markdown加载状态
  startMarkdownLoading() {
    this.isLoading = true;
    this.summarizeBtn.disabled = true;
    this.markdownBtn.disabled = true;
    this.markdownBtn.classList.add('loading');
    this.hideAllSections();
    this.loadingSection.style.display = 'flex';

    // 更新按钮文本和添加取消按钮
    const buttonText = this.markdownBtn.querySelector('span');
    if (buttonText) {
      buttonText.textContent = '正在提取...';
    }

    // 添加取消按钮到加载区域
    this.addCancelButton();

    // Markdown专用进度条动画
    let progress = 0;
    let stage = 0;
    const stages = [
      { text: '正在分析页面结构...', maxProgress: 15 },
      { text: '正在提取主要内容...', maxProgress: 35 },
      { text: '正在调用AI服务...', maxProgress: 60 },
      { text: '正在转换为Markdown...', maxProgress: 85 },
      { text: '正在生成预览...', maxProgress: 100 }
    ];

    const progressInterval = setInterval(() => {
      const currentStage = stages[stage];
      const increment = Math.random() * 2 + 0.5;
      progress = Math.min(progress + increment, currentStage.maxProgress);

      this.progressFill.style.width = `${progress}%`;
      this.progressText.textContent = `${Math.round(progress)}%`;

      // 更新加载文本
      const loadingText = document.querySelector('.loading-text');
      if (loadingText) {
        loadingText.textContent = currentStage.text;
      }

      // 切换到下一阶段
      if (progress >= currentStage.maxProgress && stage < stages.length - 1) {
        stage++;
      }

      if (!this.isLoading) {
        clearInterval(progressInterval);
        this.progressFill.style.width = '100%';
        this.progressText.textContent = '100%';

        // 完成动画
        setTimeout(() => {
          if (loadingText) {
            loadingText.textContent = 'Markdown提取完成！';
          }
        }, 200);
      }
    }, 400);

    this.progressInterval = progressInterval;
  }

  // 停止Markdown加载状态
  stopMarkdownLoading() {
    this.isLoading = false;
    this.summarizeBtn.disabled = false;
    this.markdownBtn.disabled = false;
    this.markdownBtn.classList.remove('loading');
    this.loadingSection.style.display = 'none';

    // 恢复按钮文本
    const buttonText = this.markdownBtn.querySelector('span');
    if (buttonText) {
      buttonText.textContent = '提取Markdown';
    }

    // 移除取消按钮
    this.removeCancelButton();

    // 清理进度条定时器
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }

    // 重置进度条
    this.progressFill.style.width = '0%';
    this.progressText.textContent = '0%';
  }

  // 显示结果
  showResult(summary, templateName) {
    this.hideAllSections();
    this.resultSection.style.display = 'block';

    // 存储当前总结数据，供弹出窗口使用
    this.currentSummary = summary;
    this.currentTemplate = templateName;

    // 格式化并显示总结内容
    this.resultContent.innerHTML = this.formatSummary(summary);

    // 显示元信息
    const now = new Date().toLocaleString('zh-CN');
    this.resultMeta.innerHTML = `
      <span>模板: ${templateName}</span>
      <span>生成时间: ${now}</span>
    `;

    // 设置滚动监听
    this.setupScrollIndicators();

    // 显示滚动提示（如果内容超出可视区域）
    this.showScrollHintIfNeeded();

    // 自动保存结果
    this.autoSaveResult(summary, templateName);

    // 滚动到结果区域
    this.resultSection.scrollIntoView({ behavior: 'smooth' });
  }

  // 格式化总结内容
  formatSummary(summary) {
    // 确保内容不为空
    if (!summary || typeof summary !== 'string') {
      return '<p>暂无内容</p>';
    }

    // 预处理：清理多余的空行和空格
    let content = summary
      .trim()
      .replace(/\r\n/g, '\n') // 统一换行符
      .replace(/\n{3,}/g, '\n\n') // 合并多个空行为两个
      .replace(/[ \t]+$/gm, ''); // 移除行尾空格

    // 简单的Markdown格式化，保持原有逻辑但增强处理
    content = content
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^\* (.*$)/gm, '<li>$1</li>')
      .replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^(.*)$/gm, '<p>$1</p>')
      .replace(/<p><li>/g, '<ul><li>')
      .replace(/<\/li><\/p>/g, '</li></ul>')
      .replace(/<p><h([1-6])>/g, '<h$1>')
      .replace(/<\/h([1-6])><\/p>/g, '</h$1>')
      .replace(/<p><\/p>/g, '') // 移除空段落
      .replace(/<p>\s*<\/p>/g, ''); // 移除只包含空格的段落

    return content;
  }

  // 显示错误
  showError(message, details = null) {
    this.hideAllSections();
    this.errorSection.style.display = 'flex';

    // 分析错误类型并提供相应的图标和建议
    const errorInfo = this.analyzeError(message);
    this.errorMessage.innerHTML = `
      <div class="error-main">${errorInfo.message}</div>
      ${errorInfo.suggestion ? `<div class="error-suggestion">${errorInfo.suggestion}</div>` : ''}
      ${details ? `<div class="error-details">${details}</div>` : ''}
    `;

    // 更新错误图标
    const errorIcon = this.errorSection.querySelector('.error-icon svg');
    if (errorIcon) {
      errorIcon.innerHTML = errorInfo.icon;
    }

    // 添加错误动画
    this.errorSection.style.animation = 'errorShake 0.5s ease-in-out';
    setTimeout(() => {
      this.errorSection.style.animation = '';
    }, 500);
  }

  // 分析错误类型
  analyzeError(message) {
    const errorTypes = [
      {
        keywords: ['api', 'key', '401', '403', '密钥'],
        icon: '<path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" stroke="currentColor" stroke-width="2" fill="none"/><path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" fill="none"/>',
        message: 'API密钥配置错误',
        suggestion: '请检查API密钥是否正确，或前往设置页面重新配置'
      },
      {
        keywords: ['网络', 'network', 'fetch', '连接'],
        icon: '<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/><path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="2" fill="none"/><line x1="9" y1="9" x2="9.01" y2="9" stroke="currentColor" stroke-width="2"/><line x1="15" y1="9" x2="15.01" y2="9" stroke="currentColor" stroke-width="2"/>',
        message: '网络连接错误',
        suggestion: '请检查网络连接是否正常，或稍后重试'
      },
      {
        keywords: ['429', '频率', '超限'],
        icon: '<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/><polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" fill="none"/>',
        message: 'API调用频率超限',
        suggestion: '请稍等片刻后再试，或检查API配额是否充足'
      },
      {
        keywords: ['内容', '提取', '页面'],
        icon: '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" fill="none"/><polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" fill="none"/><line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/><line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>',
        message: '页面内容提取失败',
        suggestion: '请刷新页面后重试，或检查页面是否已完全加载'
      }
    ];

    const lowerMessage = message.toLowerCase();
    const matchedType = errorTypes.find(type =>
      type.keywords.some(keyword => lowerMessage.includes(keyword))
    );

    return matchedType || {
      icon: '<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/><line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/><line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>',
      message: message,
      suggestion: '如果问题持续存在，请检查扩展设置或联系技术支持'
    };
  }

  // 显示Markdown结果
  showMarkdownResult(markdown, chunks) {
    this.hideAllSections();
    this.markdownSection.style.display = 'block';

    // 存储原始Markdown内容
    this.currentMarkdown = markdown;
    this.markdownTextarea.value = markdown;

    // 渲染Markdown预览
    this.renderMarkdownPreview(markdown);

    // 显示元信息
    const now = new Date().toLocaleString('zh-CN');
    const chunkInfo = chunks > 1 ? `（分${chunks}块处理）` : '';
    this.markdownMeta.innerHTML = `
      <span>生成时间: ${now}</span>
      <span>处理方式: AI智能提取${chunkInfo}</span>
      <span>字数: ${this.countWords(markdown)}</span>
    `;

    // 默认显示预览标签
    this.showMarkdownPreview();

    // 自动保存结果
    this.autoSaveMarkdown(markdown);

    // 滚动到结果区域
    this.markdownSection.scrollIntoView({ behavior: 'smooth' });
  }

  // 渲染Markdown预览
  renderMarkdownPreview(markdown) {
    const result = this.markdownParser.parseWithTOC(markdown);

    // 如果有目录，显示目录
    let html = result.html;
    if (result.toc) {
      html = result.toc + html;
    }

    this.markdownPreview.innerHTML = html;

    // 添加目录点击事件
    this.addTOCClickHandlers();
  }

  // 添加目录点击处理
  addTOCClickHandlers() {
    const tocLinks = this.markdownPreview.querySelectorAll('.markdown-toc a');
    tocLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = this.markdownPreview.querySelector(`#${targetId}`);
        if (targetElement) {
          targetElement.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });
  }

  // 隐藏所有状态区域
  hideAllSections() {
    this.resultSection.style.display = 'none';
    this.markdownSection.style.display = 'none';
    this.loadingSection.style.display = 'none';
    this.errorSection.style.display = 'none';
  }

  // 复制结果到剪贴板
  async copyResult() {
    try {
      // 提供多种格式选择
      const formats = await this.showFormatDialog();
      if (!formats) return; // 用户取消

      let textToCopy = '';

      switch (formats.format) {
        case 'plain':
          textToCopy = this.resultContent.textContent;
          break;
        case 'markdown':
          textToCopy = this.convertToMarkdown();
          break;
        case 'formatted':
          textToCopy = this.convertToFormattedText();
          break;
        default:
          textToCopy = this.resultContent.textContent;
      }

      // 添加页面信息（如果选择）
      if (formats.includePageInfo) {
        const pageInfo = `页面标题: ${this.pageTitle.textContent}\n页面链接: ${this.pageUrl.textContent}\n生成时间: ${new Date().toLocaleString('zh-CN')}\n\n`;
        textToCopy = pageInfo + textToCopy;
      }

      await navigator.clipboard.writeText(textToCopy);
      this.showToast('已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      // 降级方案：使用传统的选择和复制方法
      this.fallbackCopy();
    }
  }

  // 降级复制方案
  fallbackCopy() {
    try {
      const range = document.createRange();
      range.selectNode(this.resultContent);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);

      const successful = document.execCommand('copy');
      selection.removeAllRanges();

      if (successful) {
        this.showToast('已复制到剪贴板');
      } else {
        this.showToast('复制失败，请手动选择文本复制');
      }
    } catch (error) {
      this.showToast('复制失败，请手动选择文本复制');
    }
  }

  // 导出结果为文件
  async exportResult() {
    try {
      // 提供多种格式选择
      const formats = await this.showExportDialog();
      if (!formats) return; // 用户取消

      let content = '';
      let filename = '';
      let mimeType = '';

      // 准备内容
      const pageInfo = formats.includePageInfo ?
        `页面标题: ${this.pageTitle.textContent}\n页面链接: ${this.pageUrl.textContent}\n生成时间: ${new Date().toLocaleString('zh-CN')}\n\n` : '';

      switch (formats.format) {
        case 'txt':
          content = pageInfo + this.resultContent.textContent;
          filename = `网页总结_${this.generateFilename()}.txt`;
          mimeType = 'text/plain;charset=utf-8';
          break;
        case 'md':
          content = pageInfo + this.convertToMarkdown();
          filename = `网页总结_${this.generateFilename()}.md`;
          mimeType = 'text/markdown;charset=utf-8';
          break;
        case 'html':
          content = this.convertToHTML(pageInfo);
          filename = `网页总结_${this.generateFilename()}.html`;
          mimeType = 'text/html;charset=utf-8';
          break;
        case 'json':
          content = JSON.stringify({
            pageTitle: this.pageTitle.textContent,
            pageUrl: this.pageUrl.textContent,
            summary: this.resultContent.textContent,
            generatedAt: new Date().toISOString(),
            template: this.resultMeta.textContent
          }, null, 2);
          filename = `网页总结_${this.generateFilename()}.json`;
          mimeType = 'application/json;charset=utf-8';
          break;
        default:
          content = pageInfo + this.resultContent.textContent;
          filename = `网页总结_${this.generateFilename()}.txt`;
          mimeType = 'text/plain;charset=utf-8';
      }

      // 创建并下载文件
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.showToast(`文件已下载: ${filename}`);
    } catch (error) {
      console.error('导出失败:', error);
      this.showToast('导出失败');
    }
  }

  // 生成文件名
  generateFilename() {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10);
    const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '-');
    const pageTitle = this.pageTitle.textContent.replace(/[^\w\u4e00-\u9fff]/g, '_').slice(0, 20);
    return `${pageTitle}_${dateStr}_${timeStr}`;
  }

  // 打开设置页面
  openSettings() {
    chrome.runtime.openOptionsPage();
  }

  // 切换主题
  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', newTheme);
    
    // 保存主题设置
    if (this.config) {
      this.config.uiSettings = this.config.uiSettings || {};
      this.config.uiSettings.theme = newTheme;
      this.sendMessage({ action: 'saveConfig', config: { uiSettings: this.config.uiSettings } });
    }
  }

  // 应用主题
  applyTheme() {
    if (this.config && this.config.uiSettings && this.config.uiSettings.theme) {
      document.documentElement.setAttribute('data-theme', this.config.uiSettings.theme);
    }
  }

  // 保存选中的模板
  saveSelectedTemplate() {
    const selectedTemplate = this.templateSelect.value;
    if (this.config) {
      this.config.selectedTemplate = selectedTemplate;
      this.sendMessage({ action: 'saveConfig', config: { selectedTemplate } });
    }
  }

  // 复制Markdown内容
  async copyMarkdown() {
    try {
      const markdownContent = this.markdownTextarea.value;
      await navigator.clipboard.writeText(markdownContent);
      this.showToast('Markdown内容已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      this.showToast('复制失败，请手动选择文本复制', 'error');
    }
  }

  // 下载Markdown文件
  downloadMarkdown() {
    try {
      const markdownContent = this.markdownTextarea.value;
      const filename = `${this.generateFilename()}.md`;

      const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.showToast(`Markdown文件已下载: ${filename}`);
    } catch (error) {
      console.error('下载失败:', error);
      this.showToast('下载失败', 'error');
    }
  }

  // 打开独立预览窗口
  openMarkdownPreview() {
    if (!this.currentMarkdown) {
      this.showToast('没有可预览的Markdown内容', 'warning');
      return;
    }

    try {
      // 准备传递给预览窗口的数据
      const previewData = {
        content: this.currentMarkdown,
        pageInfo: this.currentPageInfo
      };

      // 将数据保存到localStorage，供预览窗口读取
      localStorage.setItem('markdown_preview_content', this.currentMarkdown);
      localStorage.setItem('markdown_preview_pageinfo', JSON.stringify(this.currentPageInfo));

      // 计算窗口位置（居中显示）- 增加窗口尺寸
      const width = Math.min(1200, screen.width * 0.9); // 最大1200px或屏幕宽度的90%
      const height = Math.min(800, screen.height * 0.9); // 最大800px或屏幕高度的90%
      const left = Math.round((screen.width - width) / 2);
      const top = Math.round((screen.height - height) / 2);

      // 打开预览窗口
      const previewWindow = window.open(
        chrome.runtime.getURL('preview/markdown-preview.html'),
        'markdownPreview',
        `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=no,toolbar=no,menubar=no,location=no`
      );

      if (previewWindow) {
        // 窗口打开成功
        this.showToast('预览窗口已打开', 'success');

        // 当预览窗口加载完成后，发送数据
        previewWindow.addEventListener('load', () => {
          previewWindow.postMessage({
            type: 'MARKDOWN_CONTENT',
            content: this.currentMarkdown,
            pageInfo: this.currentPageInfo
          }, '*');
        });
      } else {
        // 窗口被阻止
        this.showToast('预览窗口被浏览器阻止，请允许弹出窗口', 'warning');
      }
    } catch (error) {
      console.error('打开预览窗口失败:', error);
      this.showToast('打开预览窗口失败', 'error');
    }
  }

  // 切换编辑模式
  toggleMarkdownEdit() {
    if (this.sourceTab.classList.contains('active')) {
      this.showMarkdownPreview();
    } else {
      this.showMarkdownSource();
    }
  }

  // 显示Markdown预览
  showMarkdownPreview() {
    this.previewTab.classList.add('active');
    this.sourceTab.classList.remove('active');
    this.markdownPreview.style.display = 'block';
    this.markdownSource.style.display = 'none';

    // 更新预览内容
    this.renderMarkdownPreview(this.markdownTextarea.value);
  }

  // 显示Markdown源码
  showMarkdownSource() {
    this.previewTab.classList.remove('active');
    this.sourceTab.classList.add('active');
    this.markdownPreview.style.display = 'none';
    this.markdownSource.style.display = 'block';

    // 聚焦到文本区域
    this.markdownTextarea.focus();
  }

  // 更新Markdown预览
  updateMarkdownPreview() {
    if (this.previewTab.classList.contains('active')) {
      this.renderMarkdownPreview(this.markdownTextarea.value);
    }
  }

  // 自动保存Markdown结果
  autoSaveMarkdown(markdown) {
    if (!markdown) return;

    const historyItem = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      pageTitle: this.pageTitle.textContent,
      pageUrl: this.pageUrl.textContent,
      content: markdown,
      type: 'markdown',
      wordCount: this.countWords(markdown)
    };

    // 发送到background script保存
    this.sendMessage({
      action: 'addHistoryRecord',
      record: historyItem
    }).catch(error => {
      console.error('保存Markdown历史记录失败:', error);
    });
  }

  // 添加取消按钮
  addCancelButton() {
    // 检查是否已经有取消按钮
    if (document.getElementById('cancelBtn')) return;

    const cancelBtn = document.createElement('button');
    cancelBtn.id = 'cancelBtn';
    cancelBtn.className = 'secondary-btn cancel-btn';
    cancelBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
        <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
        <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
      </svg>
      <span>取消操作</span>
    `;

    cancelBtn.addEventListener('click', () => this.cancelCurrentOperation());

    // 添加到加载区域
    const loadingSection = document.getElementById('loadingSection');
    if (loadingSection) {
      loadingSection.appendChild(cancelBtn);
    }
  }

  // 移除取消按钮
  removeCancelButton() {
    const cancelBtn = document.getElementById('cancelBtn');
    if (cancelBtn) {
      cancelBtn.remove();
    }
  }

  // 取消当前操作
  cancelCurrentOperation() {
    if (this.currentOperation) {
      this.currentOperation.abort();
    }
    this.stopMarkdownLoading();
    this.showToast('操作已取消', 'info');
  }

  // 显示确认对话框
  showConfirmDialog(title, message, confirmText = '确定', cancelText = '取消') {
    return new Promise((resolve) => {
      const dialog = document.createElement('div');
      dialog.className = 'confirm-dialog-overlay';
      dialog.innerHTML = `
        <div class="confirm-dialog">
          <div class="dialog-header">
            <h3>${title}</h3>
          </div>
          <div class="dialog-content">
            <p>${message}</p>
          </div>
          <div class="dialog-actions">
            <button class="btn-secondary cancel-confirm-btn">${cancelText}</button>
            <button class="btn-primary confirm-confirm-btn">${confirmText}</button>
          </div>
        </div>
      `;

      document.body.appendChild(dialog);

      // 绑定事件
      const cancelBtn = dialog.querySelector('.cancel-confirm-btn');
      const confirmBtn = dialog.querySelector('.confirm-confirm-btn');

      cancelBtn.addEventListener('click', () => {
        dialog.remove();
        resolve(false);
      });

      confirmBtn.addEventListener('click', () => {
        dialog.remove();
        resolve(true);
      });

      // 添加样式（如果还没有）
      this.addConfirmDialogStyles();
    });
  }

  // 添加确认对话框样式
  addConfirmDialogStyles() {
    if (document.getElementById('confirm-dialog-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'confirm-dialog-styles';
    styles.textContent = `
      .confirm-dialog-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.2s ease;
      }

      .confirm-dialog {
        background: var(--bg-card);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
        width: 90%;
        max-width: 400px;
        border: 1px solid var(--border-color);
        animation: slideUp 0.3s ease;
      }

      .confirm-dialog .dialog-header {
        padding: var(--spacing-md) var(--spacing-lg);
        border-bottom: 1px solid var(--border-color);
      }

      .confirm-dialog .dialog-header h3 {
        margin: 0;
        color: var(--text-primary);
        font-size: 16px;
      }

      .confirm-dialog .dialog-content {
        padding: var(--spacing-lg);
      }

      .confirm-dialog .dialog-content p {
        margin: 0;
        color: var(--text-secondary);
        line-height: 1.6;
      }

      .confirm-dialog .dialog-actions {
        display: flex;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        border-top: 1px solid var(--border-color);
        justify-content: flex-end;
      }

      .cancel-btn {
        margin-top: var(--spacing-md);
        background: var(--bg-hover);
        border: 1px solid var(--border-color);
      }

      .cancel-btn:hover {
        background: var(--bg-secondary);
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      @keyframes slideUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
    `;
    document.head.appendChild(styles);
  }

  // 显示Toast通知
  showToast(message, type = 'success', duration = 3000) {
    this.toastMessage.textContent = message;
    this.toast.className = `toast ${type} show`;

    // 添加图标
    const iconMap = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    };

    const existingIcon = this.toast.querySelector('.toast-icon-text');
    if (existingIcon) {
      existingIcon.remove();
    }

    const iconElement = document.createElement('span');
    iconElement.className = 'toast-icon-text';
    iconElement.textContent = iconMap[type] || iconMap.info;
    this.toast.querySelector('.toast-content').insertBefore(iconElement, this.toastMessage);

    // 自动隐藏
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }

    this.toastTimeout = setTimeout(() => {
      this.toast.classList.remove('show');
    }, duration);

    // 点击关闭
    const closeHandler = () => {
      this.toast.classList.remove('show');
      this.toast.removeEventListener('click', closeHandler);
    };
    this.toast.addEventListener('click', closeHandler);
  }

  // 添加键盘快捷键支持
  initializeKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + Enter: 开始总结
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (!this.isLoading) {
          this.handleSummarize();
        }
      }

      // Ctrl/Cmd + C: 复制结果（当结果可见时）
      if ((e.ctrlKey || e.metaKey) && e.key === 'c' &&
          this.resultSection.style.display !== 'none') {
        e.preventDefault();
        this.copyResult();
      }

      // Escape: 关闭对话框或重置状态
      if (e.key === 'Escape') {
        const dialogs = document.querySelectorAll('.format-dialog-overlay');
        if (dialogs.length > 0) {
          dialogs.forEach(dialog => dialog.remove());
        } else if (this.isLoading) {
          // 可以考虑添加取消功能
        }
      }
    });
  }

  // 添加页面可见性检测
  initializeVisibilityDetection() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时暂停某些操作
        console.log('侧边栏隐藏');
      } else {
        // 页面显示时恢复操作
        console.log('侧边栏显示');
        // 可以在这里刷新页面信息
        this.updatePageInfo();
      }
    });
  }

  // 添加自动保存功能
  autoSaveResult(summary, template) {
    if (!summary) return;

    const historyItem = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      pageTitle: this.pageTitle.textContent,
      pageUrl: this.pageUrl.textContent,
      summary: summary,
      template: template,
      wordCount: this.countWords(summary)
    };

    // 发送到background script保存
    this.sendMessage({
      action: 'addHistoryRecord',
      record: historyItem
    }).catch(error => {
      console.error('保存历史记录失败:', error);
    });
  }

  // 统计词数
  countWords(text) {
    if (!text) return 0;

    // 中文字符按字符计算，英文按单词计算
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g);
    const englishWordCount = englishWords ? englishWords.length : 0;

    return chineseChars + englishWordCount;
  }

  // 设置滚动指示器
  setupScrollIndicators() {
    const content = this.resultContent;

    const updateScrollIndicators = () => {
      const { scrollTop, scrollHeight, clientHeight } = content;

      // 检查是否可以向上滚动
      if (scrollTop > 10) {
        content.classList.add('scrollable-top');
      } else {
        content.classList.remove('scrollable-top');
      }

      // 检查是否可以向下滚动
      if (scrollTop + clientHeight < scrollHeight - 10) {
        content.classList.add('scrollable-bottom');
      } else {
        content.classList.remove('scrollable-bottom');
      }
    };

    // 初始检查
    setTimeout(updateScrollIndicators, 100);

    // 监听滚动事件
    content.addEventListener('scroll', updateScrollIndicators);

    // 监听内容变化
    const resizeObserver = new ResizeObserver(updateScrollIndicators);
    resizeObserver.observe(content);

    // 存储观察器以便后续清理
    if (this.scrollObserver) {
      this.scrollObserver.disconnect();
    }
    this.scrollObserver = resizeObserver;
  }

  // 显示滚动提示（如果需要）
  showScrollHintIfNeeded() {
    const content = this.resultContent;
    const scrollHint = document.getElementById('scrollHint');

    if (!scrollHint) return;

    // 检查内容是否超出可视区域
    setTimeout(() => {
      const hasOverflow = content.scrollHeight > content.clientHeight + 10;

      if (hasOverflow) {
        scrollHint.style.display = 'block';

        // 更新提示文本，显示更多信息
        const scrollHintText = scrollHint.querySelector('.scroll-hint-text');
        if (scrollHintText) {
          const totalHeight = content.scrollHeight;
          const visibleHeight = content.clientHeight;
          const hiddenContent = Math.round(((totalHeight - visibleHeight) / totalHeight) * 100);

          scrollHintText.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M7 13l3 3 3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7 6l3 3 3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            还有约${hiddenContent}%的内容，向下滚动查看更多
          `;
        }

        // 5秒后自动隐藏提示
        setTimeout(() => {
          if (scrollHint.style.display !== 'none') {
            scrollHint.style.display = 'none';
          }
        }, 5000);

        // 用户开始滚动后隐藏提示
        const hideOnScroll = () => {
          scrollHint.style.display = 'none';
          content.removeEventListener('scroll', hideOnScroll);
        };
        content.addEventListener('scroll', hideOnScroll, { once: true });
      }
    }, 800); // 增加等待时间，确保内容完全渲染
  }

  // 添加性能监控
  performanceMonitor() {
    const startTime = performance.now();

    return {
      end: (operation) => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        console.log(`${operation} 耗时: ${duration.toFixed(2)}ms`);

        // 如果操作时间过长，显示提示
        if (duration > 10000) { // 10秒
          this.showToast(`${operation}耗时较长，请耐心等待`, 'warning', 5000);
        }
      }
    };
  }

  // 转换为Markdown格式
  convertToMarkdown() {
    const html = this.resultContent.innerHTML;
    return html
      .replace(/<h1>(.*?)<\/h1>/g, '# $1\n\n')
      .replace(/<h2>(.*?)<\/h2>/g, '## $1\n\n')
      .replace(/<h3>(.*?)<\/h3>/g, '### $1\n\n')
      .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      .replace(/<em>(.*?)<\/em>/g, '*$1*')
      .replace(/<ul><li>(.*?)<\/li><\/ul>/gs, (match, content) => {
        return content.replace(/<\/li><li>/g, '\n- ').replace(/^/, '- ') + '\n\n';
      })
      .replace(/<ol><li>(.*?)<\/li><\/ol>/gs, (match, content) => {
        const items = content.split('</li><li>');
        return items.map((item, index) => `${index + 1}. ${item}`).join('\n') + '\n\n';
      })
      .replace(/<p>(.*?)<\/p>/g, '$1\n\n')
      .replace(/<br\s*\/?>/g, '\n')
      .replace(/<[^>]*>/g, '') // 移除剩余的HTML标签
      .replace(/\n{3,}/g, '\n\n') // 合并多余的换行
      .trim();
  }

  // 转换为格式化文本
  convertToFormattedText() {
    const html = this.resultContent.innerHTML;
    return html
      .replace(/<h[1-6]>(.*?)<\/h[1-6]>/g, '\n【$1】\n')
      .replace(/<strong>(.*?)<\/strong>/g, '【$1】')
      .replace(/<em>(.*?)<\/em>/g, '《$1》')
      .replace(/<ul><li>/g, '\n• ')
      .replace(/<ol><li>/g, '\n1. ')
      .replace(/<\/li>/g, '')
      .replace(/<\/[uo]l>/g, '\n')
      .replace(/<p>/g, '\n')
      .replace(/<\/p>/g, '\n')
      .replace(/<br\s*\/?>/g, '\n')
      .replace(/<[^>]*>/g, '') // 移除剩余的HTML标签
      .replace(/\n{3,}/g, '\n\n') // 合并多余的换行
      .trim();
  }

  // 转换为HTML格式
  convertToHTML(pageInfo) {
    const content = this.resultContent.innerHTML;
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页总结 - ${this.pageTitle.textContent}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        .page-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007acc;
        }
        h1, h2, h3 { color: #007acc; }
        ul, ol { padding-left: 20px; }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="page-info">
        <pre>${pageInfo}</pre>
    </div>
    <div class="content">
        ${content}
    </div>
    <div class="footer">
        由智能网页总结助手生成 | 基于通义千问AI
    </div>
</body>
</html>`;
  }

  // 显示格式选择对话框（复制）
  showFormatDialog() {
    return new Promise((resolve) => {
      const dialog = document.createElement('div');
      dialog.className = 'format-dialog-overlay';
      dialog.innerHTML = `
        <div class="format-dialog">
          <div class="dialog-header">
            <h3>选择复制格式</h3>
            <button class="close-btn">×</button>
          </div>
          <div class="dialog-content">
            <div class="format-options">
              <label><input type="radio" name="format" value="plain" checked> 纯文本</label>
              <label><input type="radio" name="format" value="markdown"> Markdown格式</label>
              <label><input type="radio" name="format" value="formatted"> 格式化文本</label>
            </div>
            <div class="extra-options">
              <label><input type="checkbox" id="includePageInfo" checked> 包含页面信息</label>
            </div>
          </div>
          <div class="dialog-actions">
            <button class="btn-secondary cancel-btn">取消</button>
            <button class="btn-primary confirm-btn">确定</button>
          </div>
        </div>
      `;

      document.body.appendChild(dialog);

      // 绑定关闭按钮事件
      const closeBtn = dialog.querySelector('.close-btn');
      closeBtn.addEventListener('click', () => {
        dialog.remove();
        resolve(null);
      });

      // 绑定取消按钮事件
      const cancelBtn = dialog.querySelector('.cancel-btn');
      cancelBtn.addEventListener('click', () => {
        dialog.remove();
        resolve(null);
      });

      // 绑定确定按钮事件
      const confirmBtn = dialog.querySelector('.confirm-btn');
      confirmBtn.addEventListener('click', () => {
        const format = dialog.querySelector('input[name=format]:checked').value;
        const includePageInfo = dialog.querySelector('#includePageInfo').checked;
        dialog.remove();
        resolve({format, includePageInfo});
      });

      // 添加样式
      if (!document.getElementById('format-dialog-styles')) {
        const styles = document.createElement('style');
        styles.id = 'format-dialog-styles';
        styles.textContent = `
          .format-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
          }
          .format-dialog {
            background: var(--bg-card);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            width: 90%;
            max-width: 400px;
            border: 1px solid var(--border-color);
          }
          .dialog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
          }
          .dialog-header h3 {
            margin: 0;
            color: var(--text-primary);
          }
          .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--text-muted);
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .dialog-content {
            padding: var(--spacing-lg);
          }
          .format-options {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
          }
          .format-options label,
          .extra-options label {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-primary);
            cursor: pointer;
          }
          .dialog-actions {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-md) var(--spacing-lg);
            border-top: 1px solid var(--border-color);
            justify-content: flex-end;
          }
          .btn-primary, .btn-secondary {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            font-size: 14px;
          }
          .btn-primary {
            background: var(--gradient-primary);
            color: white;
          }
          .btn-secondary {
            background: var(--bg-hover);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
          }
        `;
        document.head.appendChild(styles);
      }
    });
  }

  // 显示导出格式对话框
  showExportDialog() {
    return new Promise((resolve) => {
      const dialog = document.createElement('div');
      dialog.className = 'format-dialog-overlay';
      dialog.innerHTML = `
        <div class="format-dialog">
          <div class="dialog-header">
            <h3>选择导出格式</h3>
            <button class="close-btn">×</button>
          </div>
          <div class="dialog-content">
            <div class="format-options">
              <label><input type="radio" name="format" value="txt" checked> 文本文件 (.txt)</label>
              <label><input type="radio" name="format" value="md"> Markdown文件 (.md)</label>
              <label><input type="radio" name="format" value="html"> HTML文件 (.html)</label>
              <label><input type="radio" name="format" value="json"> JSON文件 (.json)</label>
            </div>
            <div class="extra-options">
              <label><input type="checkbox" id="includePageInfoExport" checked> 包含页面信息</label>
            </div>
          </div>
          <div class="dialog-actions">
            <button class="btn-secondary cancel-btn">取消</button>
            <button class="btn-primary export-btn">导出</button>
          </div>
        </div>
      `;

      document.body.appendChild(dialog);

      // 绑定关闭按钮事件
      const closeBtn = dialog.querySelector('.close-btn');
      closeBtn.addEventListener('click', () => {
        dialog.remove();
        resolve(null);
      });

      // 绑定取消按钮事件
      const cancelBtn = dialog.querySelector('.cancel-btn');
      cancelBtn.addEventListener('click', () => {
        dialog.remove();
        resolve(null);
      });

      // 绑定导出按钮事件
      const exportBtn = dialog.querySelector('.export-btn');
      exportBtn.addEventListener('click', () => {
        const format = dialog.querySelector('input[name=format]:checked').value;
        const includePageInfo = dialog.querySelector('#includePageInfoExport').checked;
        dialog.remove();
        resolve({format, includePageInfo});
      });
    });
  }

  // 发送消息到background script
  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  // ==================== UI权限控制相关方法 ====================

  // 设置权限变化监听器
  setupPermissionListener() {
    this.permissionManager.addPermissionListener((isAuthenticated, permissionLevel) => {
      console.log(`权限状态变化: ${isAuthenticated ? '已认证' : '未认证'}, 权限级别: ${permissionLevel}`);

      // 可以在这里添加额外的权限变化处理逻辑
      if (isAuthenticated) {
        this.showToast('功能已解锁，可以正常使用所有功能', 'success', 2000);
      } else {
        this.showToast('请登录钉钉账户以使用完整功能', 'info', 3000);
      }
    });
  }

  // 更新UI权限状态
  updateUIPermissions(isAuthenticated, userInfo = null) {
    this.permissionManager.setAuthenticationStatus(isAuthenticated, userInfo);
  }

  // ==================== 钉钉认证相关方法 ====================

  // 初始化钉钉认证
  async initializeDingTalkAuth() {
    try {
      console.log('初始化钉钉认证状态...');
      await this.updateDingTalkAuthStatus();

      // 设置实时监听认证状态变化
      this.setupDingTalkAuthListener();

      // 设置定期检查认证状态（作为备用机制，延长间隔）
      setInterval(() => {
        this.updateDingTalkAuthStatus();
      }, 300000); // 每5分钟检查一次作为备用

    } catch (error) {
      console.error('初始化钉钉认证失败:', error);
      this.showDingTalkAuthError('初始化认证状态失败');
    }
  }

  // 设置钉钉认证状态实时监听
  setupDingTalkAuthListener() {
    // 监听来自background script的认证状态变化消息
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'DINGTALK_AUTH_STATUS_CHANGED') {
          console.log('收到钉钉认证状态变化通知:', message.data);

          // 立即更新本地状态和UI
          this.dingTalkAuthStatus = message.data;
          this.renderDingTalkAuthStatus();

          // 显示状态变化提示
          if (message.data.isAuthenticated) {
            this.showToast('🎉 钉钉登录成功！', 'success', 3000);
            console.log('钉钉登录状态更新成功，用户信息:', message.data.userInfo);
          } else {
            this.showToast('钉钉账户已退出登录', 'info', 3000);
            console.log('钉钉登出状态更新成功');
          }

          return true; // 保持消息通道开放
        }
      });

      console.log('钉钉认证状态实时监听器已设置');
    } else {
      console.warn('无法设置认证状态监听器，Chrome API不可用');
    }
  }

  // 更新钉钉认证状态显示
  async updateDingTalkAuthStatus() {
    try {
      const response = await this.sendMessage({ action: 'getDingTalkAuthStatus' });

      if (response.success) {
        this.dingTalkAuthStatus = response.data;
        this.renderDingTalkAuthStatus();
      } else {
        console.error('获取钉钉认证状态失败:', response.error);
        this.showDingTalkAuthError('获取认证状态失败');
      }

    } catch (error) {
      console.error('更新钉钉认证状态失败:', error);
      this.showDingTalkAuthError('更新认证状态失败');
    }
  }

  // 渲染钉钉认证状态
  renderDingTalkAuthStatus() {
    if (!this.dingTalkAuthStatus) return;

    const { isAuthenticated, userInfo, selectedOrganization } = this.dingTalkAuthStatus;

    if (isAuthenticated && userInfo) {
      // 显示已认证状态 - 简洁的"已连接"显示
      this.authNotLoggedIn.style.display = 'none';
      this.authLoggedIn.style.display = 'flex';

      // 不再显示用户详细信息，只显示"已连接"状态
      // 用户信息和组织信息已经通过新的HTML结构显示为"已连接"

    } else {
      // 显示未认证状态
      this.authNotLoggedIn.style.display = 'flex';
      this.authLoggedIn.style.display = 'none';
    }

    // 更新UI权限状态
    this.updateUIPermissions(isAuthenticated, userInfo);
  }

  // 处理钉钉登录
  async handleDingTalkLogin() {
    try {
      this.showToast('正在打开钉钉登录页面...', 'info');

      const response = await this.sendMessage({ action: 'initiateDingTalkLogin' });

      if (response.success) {
        // 显示详细的登录指引
        this.showToast('钉钉登录页面已打开', 'success', 3000);

        // 延迟显示详细指引
        setTimeout(() => {
          this.showToast('请在新页面中输入钉钉账号密码完成登录', 'info', 8000);
        }, 3000);

        setTimeout(() => {
          this.showToast('登录完成后，此处会自动显示您的用户信息', 'info', 8000);
        }, 6000);

        // 开始轮询检查认证状态（作为备用机制，实时监听是主要机制）
        this.startAuthPolling();

      } else {
        console.error('发起钉钉登录失败:', response.error);
        this.showToast('打开登录页面失败，请重试', 'error');
      }

    } catch (error) {
      console.error('处理钉钉登录失败:', error);
      this.showToast('登录操作失败，请重试', 'error');
    }
  }

  // 开始认证状态轮询（备用机制，主要依赖实时监听）
  startAuthPolling() {
    let pollCount = 0;
    const maxPolls = 24; // 最多轮询24次（2分钟）
    let lastToastTime = 0;

    const pollInterval = setInterval(async () => {
      pollCount++;

      try {
        await this.updateDingTalkAuthStatus();

        // 如果已认证，停止轮询（实时监听应该已经处理了，这里是备用）
        if (this.dingTalkAuthStatus && this.dingTalkAuthStatus.isAuthenticated) {
          clearInterval(pollInterval);
          // 不再显示成功提示，因为实时监听已经显示了
          console.log('轮询检测到登录成功（实时监听应该已经处理）');
          return;
        }

        // 每15秒显示一次提示（避免过于频繁）
        const now = Date.now();
        if (now - lastToastTime > 15000) {
          if (pollCount <= 6) {
            this.showToast(`正在检测登录状态... 请确保已在新页面中完成登录`, 'info', 3000);
          } else if (pollCount <= 18) {
            this.showToast(`仍在等待登录... 如已登录，请稍等片刻`, 'info', 3000);
          } else {
            this.showToast(`登录检测中... 您也可以手动刷新认证状态`, 'info', 3000);
          }
          lastToastTime = now;
        }

        // 超过最大轮询次数，停止轮询
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval);
          this.showToast('登录检测超时。如果您已完成登录，请点击用户头像旁的刷新按钮', 'warning', 8000);
        }

      } catch (error) {
        console.error('轮询认证状态失败:', error);
        clearInterval(pollInterval);
        this.showToast('认证状态检测出错，请手动刷新', 'error');
      }
    }, 10000); // 每10秒检查一次（备用机制，主要依赖实时监听）
  }

  // 处理钉钉状态刷新
  async handleDingTalkRefresh() {
    try {
      this.showToast('正在刷新认证状态...', 'info');

      const response = await this.sendMessage({ action: 'forceDingTalkAuthCheck' });

      if (response.success) {
        const { authStatus, cookieValid, actionTaken } = response.data;

        // 更新本地状态
        this.dingTalkAuthStatus = authStatus;
        this.renderDingTalkAuthStatus();

        // 根据执行的操作显示不同的提示
        if (actionTaken === 'triggered_login') {
          this.showToast('🎉 检测到登录状态，已更新显示！', 'success');
        } else if (actionTaken === 'triggered_logout') {
          this.showToast('检测到登出状态，已更新显示', 'info');
        } else {
          this.showToast('认证状态已刷新', 'success');
        }

        console.log('认证状态刷新完成:', {
          isAuthenticated: authStatus.isAuthenticated,
          cookieValid: cookieValid,
          actionTaken: actionTaken
        });

      } else {
        console.error('刷新认证状态失败:', response.error);
        this.showToast('刷新认证状态失败，请重试', 'error');
      }

    } catch (error) {
      console.error('处理认证状态刷新失败:', error);
      this.showToast('刷新操作失败，请重试', 'error');
    }
  }

  // 处理钉钉登出
  async handleDingTalkLogout() {
    try {
      const confirmed = await this.showConfirmDialog(
        '确认登出',
        '确定要退出钉钉登录吗？退出后将无法使用钉钉相关功能。'
      );

      if (!confirmed) return;

      this.showToast('正在退出登录...', 'info');

      const response = await this.sendMessage({ action: 'dingTalkLogout' });

      if (response.success) {
        this.showToast('已成功退出钉钉登录', 'success');
        await this.updateDingTalkAuthStatus();
      } else {
        console.error('钉钉登出失败:', response.error);
        this.showToast('退出登录失败，请重试', 'error');
      }

    } catch (error) {
      console.error('处理钉钉登出失败:', error);
      this.showToast('退出登录操作失败', 'error');
    }
  }

  // 打开钉钉设置
  openDingTalkSettings() {
    // 打开设置页面并跳转到钉钉配置标签
    const settingsUrl = chrome.runtime.getURL('options/options.html?tab=dingtalk');
    chrome.tabs.create({ url: settingsUrl });
  }

  // 显示钉钉认证错误
  showDingTalkAuthError(message) {
    // 在认证状态区域显示错误信息
    this.authNotLoggedIn.style.display = 'flex';
    this.authLoggedIn.style.display = 'none';

    // 临时显示错误信息
    const originalText = this.authNotLoggedIn.querySelector('.auth-description').textContent;
    this.authNotLoggedIn.querySelector('.auth-description').textContent = message;
    this.authNotLoggedIn.querySelector('.auth-description').style.color = '#ff4d4f';

    // 3秒后恢复原始文本
    setTimeout(() => {
      this.authNotLoggedIn.querySelector('.auth-description').textContent = originalText;
      this.authNotLoggedIn.querySelector('.auth-description').style.color = '';
    }, 3000);
  }

  // 刷新钉钉认证状态
  async refreshDingTalkAuth() {
    try {
      const response = await this.sendMessage({ action: 'refreshDingTalkAuth' });

      if (response.success) {
        this.dingTalkAuthStatus = response.data;
        this.renderDingTalkAuthStatus();
        return true;
      } else {
        console.error('刷新钉钉认证状态失败:', response.error);
        return false;
      }

    } catch (error) {
      console.error('刷新钉钉认证状态失败:', error);
      return false;
    }
  }

  // 显示钉钉登录帮助
  showDingTalkLoginHelp() {
    const helpContent = `
📋 钉钉登录流程说明：

1️⃣ 点击"登录钉钉"按钮
   → 系统会打开钉钉文档登录页面

2️⃣ 在新页面中完成登录
   → 输入钉钉账号密码
   → 完成验证（如需要）

3️⃣ 确认登录成功
   → 页面显示钉钉文档首页
   → 顶部显示您的用户名

4️⃣ 返回扩展查看状态
   → 等待几秒钟自动更新
   → 或手动点击刷新按钮

❓ 如果登录后状态未更新：
• 请等待10-15秒
• 确保在新页面中完成了登录
• 检查钉钉文档页面是否显示用户信息
• 尝试手动刷新认证状态

💡 需要更多帮助？
请查看详细的登录指南文档。
    `.trim();

    // 创建帮助对话框
    this.showHelpDialog('钉钉登录帮助', helpContent);
  }

  // 显示帮助对话框
  showHelpDialog(title, content) {
    // 创建对话框元素
    const dialog = document.createElement('div');
    dialog.className = 'help-dialog-overlay';
    dialog.innerHTML = `
      <div class="help-dialog">
        <div class="help-dialog-header">
          <h3>${title}</h3>
          <button class="help-dialog-close" onclick="this.closest('.help-dialog-overlay').remove()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
              <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </div>
        <div class="help-dialog-content">
          <pre>${content}</pre>
        </div>
        <div class="help-dialog-footer">
          <button class="btn btn-secondary" onclick="this.closest('.help-dialog-overlay').remove()">
            关闭
          </button>
          <button class="btn btn-primary" onclick="window.open('docs/钉钉登录流程说明.md', '_blank')">
            查看详细文档
          </button>
        </div>
      </div>
    `;

    // 添加样式
    if (!document.getElementById('help-dialog-styles')) {
      const styles = document.createElement('style');
      styles.id = 'help-dialog-styles';
      styles.textContent = `
        .help-dialog-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
        }

        .help-dialog {
          background: var(--bg-card);
          border-radius: 8px;
          max-width: 500px;
          max-height: 80vh;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .help-dialog-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px 20px;
          border-bottom: 1px solid var(--border-color);
        }

        .help-dialog-header h3 {
          margin: 0;
          color: var(--text-primary);
        }

        .help-dialog-close {
          background: none;
          border: none;
          cursor: pointer;
          color: var(--text-secondary);
          padding: 4px;
          border-radius: 4px;
        }

        .help-dialog-close:hover {
          background: var(--bg-secondary);
          color: var(--text-primary);
        }

        .help-dialog-content {
          padding: 20px;
          max-height: 400px;
          overflow-y: auto;
        }

        .help-dialog-content pre {
          white-space: pre-wrap;
          font-family: inherit;
          margin: 0;
          color: var(--text-primary);
          line-height: 1.6;
        }

        .help-dialog-footer {
          display: flex;
          gap: 8px;
          padding: 16px 20px;
          border-top: 1px solid var(--border-color);
          justify-content: flex-end;
        }

        .help-dialog .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        }

        .help-dialog .btn-secondary {
          background: var(--bg-secondary);
          color: var(--text-primary);
        }

        .help-dialog .btn-primary {
          background: #1890ff;
          color: white;
        }

        .help-dialog .btn:hover {
          opacity: 0.9;
        }
      `;
      document.head.appendChild(styles);
    }

    // 添加到页面
    document.body.appendChild(dialog);

    // 点击背景关闭
    dialog.addEventListener('click', (e) => {
      if (e.target === dialog) {
        dialog.remove();
      }
    });
  }

  // 显示钉钉调试信息
  async showDingTalkDebugInfo() {
    try {
      this.showToast('正在获取调试信息...', 'info', 2000);

      // 获取调试信息
      const response = await this.sendMessage({
        type: 'getDingTalkDebugInfo'
      });

      if (response.success) {
        const debugInfo = response.data;
        console.log('钉钉认证调试信息:', debugInfo);

        // 显示简化的调试信息
        const summary = `
认证状态: ${debugInfo.memoryState.isAuthenticated ? '已认证' : '未认证'}
用户信息: ${debugInfo.memoryState.hasUserInfo ? debugInfo.memoryState.userInfoName : '无'}
Cookie状态: ${debugInfo.cookieState.hasAccountCookie ? '有效' : '无效'}
监听器状态: ${debugInfo.listenerStatus.cookieListenerSetup ? '已设置' : '未设置'}
状态一致性: ${debugInfo.consistency.memoryStorageMatch && debugInfo.consistency.cookieMemoryMatch ? '一致' : '不一致'}
        `;

        this.showToast('调试信息已输出到控制台', 'success', 3000);
        alert('钉钉认证调试信息:\n' + summary + '\n\n详细信息请查看浏览器控制台');

      } else {
        this.showToast('获取调试信息失败: ' + response.error, 'error', 3000);
      }
    } catch (error) {
      this.showToast('获取调试信息失败: ' + error.message, 'error', 3000);
    }
  }

  // 在新窗口中打开总结结果
  async openSummaryPopup() {
    try {
      // 检查是否有总结结果
      if (!this.currentSummary) {
        this.showToast('没有可显示的总结结果', 'warning');
        return;
      }

      // 生成唯一的数据ID
      const dataId = Date.now().toString();

      // 准备总结数据
      const summaryData = {
        dataId: dataId,
        summary: this.currentSummary,
        template: this.currentTemplate || '默认模板',
        title: this.pageTitle.textContent || '未知页面',
        url: this.pageUrl.textContent || '',
        timestamp: Date.now()
      };

      // 将数据存储到Chrome本地存储和localStorage作为备份
      await chrome.storage.local.set({
        [`summary_${dataId}`]: summaryData
      });

      // 同时存储到localStorage作为备份
      localStorage.setItem(`summary_${dataId}`, JSON.stringify(summaryData));

      // 创建弹出窗口
      const popupUrl = chrome.runtime.getURL(`popup/summary-popup.html?dataId=${dataId}`);

      await chrome.windows.create({
        url: popupUrl,
        type: 'popup',
        width: 900,
        height: 700,
        focused: true
      });

      this.showToast('已在新窗口中打开总结结果', 'success');

    } catch (error) {
      console.error('打开弹出窗口失败:', error);
      this.showToast('打开新窗口失败，请检查浏览器权限', 'error');
    }
  }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  new SidebarApp();
});
