class SummaryPopup {
  constructor() {
    this.summaryData = null;
    this.initializeElements();
    this.bindEvents();
    this.loadSummaryData();
  }

  // 初始化DOM元素
  initializeElements() {
    // 主要区域
    this.loadingSection = document.getElementById('loadingSection');
    this.errorSection = document.getElementById('errorSection');
    this.contentSection = document.getElementById('contentSection');
    
    // 内容元素
    this.pageTitle = document.getElementById('pageTitle');
    this.pageUrl = document.getElementById('pageUrl');
    this.summaryContent = document.getElementById('summaryContent');
    this.metaInfo = document.getElementById('metaInfo');
    this.errorMessage = document.getElementById('errorMessage');
    
    // 按钮元素
    this.copyBtn = document.getElementById('copyBtn');
    this.exportBtn = document.getElementById('exportBtn');
    this.printBtn = document.getElementById('printBtn');
    this.closeBtn = document.getElementById('closeBtn');
    this.retryBtn = document.getElementById('retryBtn');
    this.backToSidebarBtn = document.getElementById('backToSidebarBtn');
    
    // Toast元素
    this.toast = document.getElementById('toast');
    this.toastMessage = document.getElementById('toastMessage');
  }

  // 绑定事件
  bindEvents() {
    this.copyBtn.addEventListener('click', () => this.copyToClipboard());
    this.exportBtn.addEventListener('click', () => this.exportToFile());
    this.printBtn.addEventListener('click', () => this.printContent());
    this.closeBtn.addEventListener('click', () => this.closeWindow());
    this.retryBtn.addEventListener('click', () => this.loadSummaryData());
    this.backToSidebarBtn.addEventListener('click', () => this.backToSidebar());
    
    // 键盘快捷键
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    
    // 窗口关闭事件
    window.addEventListener('beforeunload', () => this.cleanup());
  }

  // 加载总结数据
  async loadSummaryData() {
    try {
      this.showLoading();
      
      // 从URL参数获取数据ID
      const urlParams = new URLSearchParams(window.location.search);
      const dataId = urlParams.get('dataId');
      
      if (!dataId) {
        throw new Error('未找到总结数据ID');
      }
      
      // 从Chrome存储获取数据
      let summaryData;
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get([`summary_${dataId}`]);
        summaryData = result[`summary_${dataId}`];
      } else {
        // 如果Chrome API不可用，尝试从localStorage获取
        const storedData = localStorage.getItem(`summary_${dataId}`);
        summaryData = storedData ? JSON.parse(storedData) : null;
      }
      
      if (!summaryData) {
        throw new Error('总结数据已过期或不存在');
      }
      
      this.summaryData = summaryData;
      this.displaySummary();
      
    } catch (error) {
      console.error('加载总结数据失败:', error);
      this.showError(error.message);
    }
  }

  // 显示加载状态
  showLoading() {
    this.loadingSection.style.display = 'flex';
    this.errorSection.style.display = 'none';
    this.contentSection.style.display = 'none';
  }

  // 显示错误状态
  showError(message) {
    this.loadingSection.style.display = 'none';
    this.errorSection.style.display = 'flex';
    this.contentSection.style.display = 'none';
    this.errorMessage.textContent = message;
  }

  // 显示总结内容
  displaySummary() {
    if (!this.summaryData) return;
    
    this.loadingSection.style.display = 'none';
    this.errorSection.style.display = 'none';
    this.contentSection.style.display = 'block';
    
    // 设置页面信息
    this.pageTitle.textContent = this.summaryData.title || '未知页面';
    this.pageUrl.textContent = this.summaryData.url || '';
    this.pageUrl.title = this.summaryData.url || '';
    
    // 设置总结内容
    this.summaryContent.innerHTML = this.formatSummary(this.summaryData.summary);
    
    // 设置元信息
    this.metaInfo.innerHTML = this.formatMetaInfo();
    
    // 更新窗口标题
    document.title = `总结结果 - ${this.summaryData.title || '智能网页总结助手'}`;
  }

  // 格式化总结内容
  formatSummary(summary) {
    if (!summary || typeof summary !== 'string') {
      return '<p>暂无内容</p>';
    }

    // 基本的Markdown样式转换
    let formatted = summary
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^\* (.*$)/gm, '<li>$1</li>')
      .replace(/^\d+\. (.*$)/gm, '<li>$1</li>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>');

    // 包装段落
    if (!formatted.startsWith('<h') && !formatted.startsWith('<li')) {
      formatted = '<p>' + formatted + '</p>';
    }

    // 包装列表
    formatted = formatted.replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');

    return formatted;
  }

  // 格式化元信息
  formatMetaInfo() {
    if (!this.summaryData) return '';
    
    const template = this.summaryData.template || '默认模板';
    const timestamp = this.summaryData.timestamp || Date.now();
    const generateTime = new Date(timestamp).toLocaleString('zh-CN');
    const wordCount = this.countWords(this.summaryData.summary || '');
    
    return `
      <span>📝 模板: ${template}</span>
      <span>🕒 生成时间: ${generateTime}</span>
      <span>📊 字数: ${wordCount}</span>
    `;
  }

  // 统计字数
  countWords(text) {
    if (!text) return 0;
    // 移除HTML标签和特殊字符，统计中文字符和英文单词
    const cleanText = text.replace(/<[^>]*>/g, '').replace(/[^\u4e00-\u9fa5\w\s]/g, '');
    const chineseChars = (cleanText.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (cleanText.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }

  // 复制到剪贴板
  async copyToClipboard() {
    if (!this.summaryData) return;
    
    try {
      const textContent = this.summaryContent.textContent || this.summaryData.summary;
      await navigator.clipboard.writeText(textContent);
      this.showToast('已复制到剪贴板', 'success');
    } catch (error) {
      console.error('复制失败:', error);
      this.showToast('复制失败，请手动选择文本复制', 'error');
    }
  }

  // 导出为文件
  exportToFile() {
    if (!this.summaryData) return;
    
    try {
      const content = this.summaryData.summary || '';
      const title = this.summaryData.title || '网页总结';
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `${title}_总结_${timestamp}.txt`;
      
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      this.showToast('文件导出成功', 'success');
    } catch (error) {
      console.error('导出失败:', error);
      this.showToast('导出失败，请重试', 'error');
    }
  }

  // 打印内容
  printContent() {
    window.print();
  }

  // 关闭窗口
  closeWindow() {
    window.close();
  }

  // 返回侧边栏
  async backToSidebar() {
    try {
      if (typeof chrome !== 'undefined' && chrome.tabs && chrome.sidePanel) {
        // 获取当前活动标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab) {
          // 打开侧边栏
          await chrome.sidePanel.open({ tabId: tab.id });
        }
      } else {
        // 如果Chrome API不可用，显示提示信息
        this.showToast('请手动返回到原始标签页查看侧边栏', 'info');
      }
      this.closeWindow();
    } catch (error) {
      console.error('返回侧边栏失败:', error);
      this.showToast('返回侧边栏失败，请手动返回原始标签页', 'warning');
      this.closeWindow();
    }
  }

  // 处理键盘快捷键
  handleKeyboard(e) {
    // Ctrl/Cmd + C: 复制
    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
      e.preventDefault();
      this.copyToClipboard();
    }
    
    // Ctrl/Cmd + P: 打印
    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
      e.preventDefault();
      this.printContent();
    }
    
    // Escape: 关闭窗口
    if (e.key === 'Escape') {
      this.closeWindow();
    }
  }

  // 显示Toast通知
  showToast(message, type = 'success') {
    this.toastMessage.textContent = message;
    this.toast.className = `toast ${type}`;
    this.toast.classList.add('show');
    
    setTimeout(() => {
      this.toast.classList.remove('show');
    }, 3000);
  }

  // 清理资源
  cleanup() {
    if (this.summaryData && this.summaryData.dataId) {
      // 清理临时存储的数据
      try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
          chrome.storage.local.remove([`summary_${this.summaryData.dataId}`]);
        }
        // 同时清理localStorage
        localStorage.removeItem(`summary_${this.summaryData.dataId}`);
      } catch (error) {
        console.error('清理存储数据失败:', error);
      }
    }
  }
}

// 初始化弹出窗口
document.addEventListener('DOMContentLoaded', () => {
  new SummaryPopup();
});
