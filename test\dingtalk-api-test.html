<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉API文档总结测试</title>
    <style>
        body {
            margin: 20px;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #007acc;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a9e;
        }
        .output {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: #fafafa;
            min-height: 200px;
            max-height: 600px;
            overflow-y: auto;
        }
        .markdown-table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        .markdown-table th,
        .markdown-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .markdown-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        blockquote {
            border-left: 4px solid #007acc;
            margin: 10px 0;
            padding: 10px 20px;
            background: #f9f9f9;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 2px;
            font-family: 'Courier New', monospace;
        }
        pre code {
            background: none;
            padding: 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .before, .after {
            padding: 15px;
            border-radius: 4px;
        }
        .before {
            background: #fff5f5;
            border: 1px solid #ffcccc;
        }
        .after {
            background: #f5fff5;
            border: 1px solid #ccffcc;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>钉钉API文档总结测试</h1>
        <p>测试修复后的技术文档模板是否能正确保留"返回示例"等重要技术信息</p>
        
        <div class="test-section">
            <h3>模拟AI总结结果对比</h3>
            <div class="controls">
                <button onclick="showOldResult()">显示修复前效果</button>
                <button onclick="showNewResult()">显示修复后效果</button>
                <button onclick="testCopyFunction()">测试复制功能</button>
            </div>
            
            <div class="comparison">
                <div class="before">
                    <h4>❌ 修复前（丢失返回示例）</h4>
                    <div id="oldResult" class="output"></div>
                </div>
                <div class="after">
                    <h4>✅ 修复后（保留完整信息）</h4>
                    <div id="newResult" class="output"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>技术文档模板改进说明</h3>
            <div class="output">
                <h4>主要改进点：</h4>
                <ul>
                    <li><strong>明确要求保留代码示例</strong>：在模板中明确指出要保留所有代码块和返回示例</li>
                    <li><strong>增加返回示例章节</strong>：在模板结构中专门添加"返回示例"部分</li>
                    <li><strong>强调技术细节</strong>：要求AI不要省略或简化技术参数和配置信息</li>
                    <li><strong>格式化指导</strong>：明确要求使用代码块格式保持示例的准确性</li>
                </ul>
                
                <h4>新的技术文档模板结构：</h4>
                <ol>
                    <li>主要功能特性</li>
                    <li>使用方法</li>
                    <li><strong>代码示例</strong> ← 新增</li>
                    <li><strong>参数说明</strong> ← 新增</li>
                    <li><strong>返回示例</strong> ← 新增重点</li>
                    <li>注意事项</li>
                    <li>适用场景</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../libs/markdown-parser.js"></script>
    <script>
        const parser = new MarkdownParser();

        // 模拟修复前的总结结果（丢失返回示例）
        const oldSummaryResult = `# 创建钉钉待办任务API

## 主要功能特性

- 支持创建工作待办和个人待办
- 可设置执行者、参与者和截止时间
- 支持优先级设置和通知配置
- 提供详情页跳转功能

## 使用方法

1. 获取访问凭证
2. 构造请求参数
3. 发送POST请求到指定端点
4. 处理返回结果

## 注意事项

- 需要申请待办写权限
- detailUrl字段为必填项
- 支持多种应用类型

## 适用场景

适用于企业内部应用和第三方企业应用的待办任务管理。`;

        // 模拟修复后的总结结果（保留完整信息）
        const newSummaryResult = `# 创建钉钉待办任务API

## 主要功能特性

- **工作待办创建**：支持创建第三方业务自闭环场景的待办任务
- **个人待办创建**：支持创建与用户在钉钉客户端创建的待办完全一致的任务
- **多角色支持**：可设置执行者、参与者和创建者
- **时间管理**：支持截止时间、提醒时间设置
- **优先级控制**：支持4个优先级等级（较低、普通、紧急、非常紧急）
- **通知配置**：支持钉钉消息通知和提醒设置

## 使用方法

1. **权限申请**：申请"待办应用中待办写权限"
2. **获取访问凭证**：通过统一授权套件获取access_token
3. **构造请求**：
   - 请求方法：POST
   - 端点：\`/v1.0/todo/users/{unionId}/tasks\`
   - 请求头：\`x-acs-dingtalk-access-token\`

## 代码示例

### 请求示例

\`\`\`http
POST /v1.0/todo/users/PUoiinWIpa2yH2ymhiiGiP6g/tasks?operatorId=PUoiinWIpxxx HTTP/1.1
Host: api.dingtalk.com
x-acs-dingtalk-access-token: xxxxx
Content-Type: application/json

{
  "sourceId": "isv_dingtalkTodo1",
  "subject": "接入钉钉待办",
  "creatorId": "PUoiinWIpa2yH2ymhiiGiP6g",
  "description": "应用可以调用该接口发起一个钉钉待办任务...",
  "dueTime": 1617675000000,
  "executorIds": ["PUoiinWIpa2yH2ymhiiGiP6g"],
  "participantIds": ["PUoiinWIpa2yH2ymhiiGiP6g"],
  "detailUrl": {
    "appUrl": "https://www.dingtalk.com",
    "pcUrl": "https://www.dingtalk.com"
  },
  "isOnlyShowExecutor": true,
  "priority": 20,
  "notifyConfigs": {
    "dingNotify": "1"
  }
}
\`\`\`

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sourceId | String | 是 | 业务系统侧的唯一标识ID |
| subject | String | 是 | 待办的标题 |
| creatorId | String | 是 | 创建者的unionId |
| executorIds | Array | 是 | 执行者的unionId列表 |
| dueTime | Long | 否 | 截止时间，Unix时间戳 |
| priority | Integer | 否 | 优先级：10(较低)、20(普通)、30(紧急)、40(非常紧急) |
| detailUrl | Object | 否 | 详情页url跳转地址 |

## 返回示例

\`\`\`json
{
  "id": "OPJpwtwPVNGIFKURjrzd",
  "subject": "接入钉钉待办",
  "description": "应用可以调用该接口发起一个钉钉待办任务...",
  "startTime": 1617675000000,
  "dueTime": 1617675100000,
  "finishTime": 1617675200000,
  "done": false,
  "executorIds": ["PUoiinWIpa2yH2ymhiiGiP6g"],
  "participantIds": ["PUoiinWIpa2yH2ymhiiGiP6g"],
  "detailUrl": {
    "pcUrl": "https://www.dingtalk.com",
    "appUrl": "https://www.dingtalk.com"
  },
  "source": "isv_dingtalkTodo",
  "sourceId": "isv_dingtalkTodo1",
  "createdTime": 1617675200000,
  "modifiedTime": 1617675200000,
  "creatorId": "PUoiinWIpa2yH2ymhiiGiP6g",
  "modifierId": "PUoiinWIpa2yH2ymhiiGiP6g",
  "bizTag": "isv_dingtalkTodo",
  "requestId": "xxx",
  "priority": 20
}
\`\`\`

## 注意事项

> **重要提醒**：从2024年2月1日起，本接口升级为仅支持创建工作待办，detailUrl字段将成为必填项。

- **权限要求**：需要申请"待办应用中待办写权限"
- **授权类型**：第三方企业应用需要申请个人授权(type=0)
- **字段限制**：
  - 待办标题长度不能超出限制
  - 执行者和参与者数量有上限
  - 截止时间必须为有效的Unix时间戳

## 适用场景

| 应用类型 | 支持情况 | 权限要求 |
|----------|----------|----------|
| 企业内部应用 | ✅ 支持 | 待办应用中待办写权限 |
| 第三方企业应用 | ✅ 支持 | 待办应用中待办写权限 |
| 第三方个人应用 | ❌ 暂不支持 | 暂不支持 |

**主要使用场景**：
- 企业内部工作流程管理
- 第三方业务系统集成
- 任务分配和跟踪
- 项目管理和协作`;

        function showOldResult() {
            const oldResult = document.getElementById('oldResult');
            oldResult.innerHTML = parser.parse(oldSummaryResult);
        }

        function showNewResult() {
            const newResult = document.getElementById('newResult');
            newResult.innerHTML = parser.parse(newSummaryResult);
        }

        async function testCopyFunction() {
            try {
                const testData = {
                    summary: newSummaryResult,
                    title: '创建钉钉待办任务 - 钉钉开放平台',
                    url: 'https://open.dingtalk.com/document/orgapp/add-dingtalk-to-do-task',
                    template: '技术文档',
                    timestamp: Date.now()
                };

                const pageInfo = `# ${testData.title}

> **来源：** ${testData.url}  
> **生成时间：** ${new Date(testData.timestamp).toLocaleString('zh-CN')}  
> **模板：** ${testData.template}

---

`;
                
                const fullContent = pageInfo + testData.summary;
                await navigator.clipboard.writeText(fullContent);
                alert('✅ 完整的Markdown内容已复制到剪贴板！\n\n包含：\n- 页面信息头部\n- 完整的API文档总结\n- 代码示例和返回示例\n- 参数说明表格');
            } catch (error) {
                console.error('复制失败:', error);
                alert('❌ 复制失败，请手动选择文本复制');
            }
        }

        // 初始化显示
        showOldResult();
        showNewResult();
    </script>
</body>
</html>
