<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能网页总结助手</title>
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <div class="sidebar-container">
        <!-- 头部区域 -->
        <header class="sidebar-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h1 class="logo-text">智能总结</h1>
                </div>
                <div class="header-actions">
                    <button class="icon-btn" id="settingsBtn" title="设置">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                    <button class="icon-btn" id="themeToggle" title="切换主题">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2"/>
                            <line x1="12" y1="1" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                            <line x1="12" y1="21" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" stroke="currentColor" stroke-width="2"/>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" stroke="currentColor" stroke-width="2"/>
                            <line x1="1" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2"/>
                            <line x1="21" y1="12" x2="23" y2="12" stroke="currentColor" stroke-width="2"/>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" stroke="currentColor" stroke-width="2"/>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="sidebar-main">
            <!-- 钉钉认证状态区域 -->
            <section class="dingtalk-auth-status" id="dingTalkAuthStatus">
                <div class="auth-header">
                    <div class="auth-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <span class="auth-title">钉钉认证</span>
                </div>

                <!-- 未认证状态 -->
                <div class="auth-content auth-not-logged-in" id="authNotLoggedIn">
                    <div class="auth-message">
                        <span class="auth-status-text">未登录</span>
                        <span class="auth-description">登录钉钉账户以使用更多功能</span>
                    </div>
                    <div class="auth-actions">
                        <button class="auth-login-btn" id="authLoginBtn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="10,17 15,12 10,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <line x1="15" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            登录钉钉
                        </button>
                        <button class="auth-help-btn" id="authHelpBtn" title="登录帮助">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                <path d="M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <button class="auth-debug-btn" id="authDebugBtn" title="调试信息" style="background: #ff6b6b; color: white;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2"/>
                                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 已认证状态 -->
                <div class="auth-content auth-logged-in" id="authLoggedIn" style="display: none;">
                    <div class="auth-connected-status">
                        <div class="auth-status-indicator">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#52c41a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#52c41a" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="auth-status-text">
                            <span class="auth-connected-label">已连接</span>
                        </div>
                    </div>
                    <div class="auth-actions">
                        <button class="auth-settings-btn" id="authSettingsBtn" title="钉钉设置">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 页面信息显示 -->
            <section class="page-info" id="pageInfo">
                <div class="page-title" id="pageTitle">当前页面</div>
                <div class="page-url" id="pageUrl">等待加载...</div>
                <div class="page-stats" id="pageStats">
                    <span class="word-count">字数: 0</span>
                </div>
            </section>

            <!-- 功能区域容器 -->
            <div class="feature-container" id="featureContainer">
                <!-- 认证提示区域 -->
                <div class="auth-required-overlay" id="authRequiredOverlay" style="display: none;">
                    <div class="auth-required-content">
                        <div class="auth-icon">🔒</div>
                        <div class="auth-message">
                            <h4>需要登录验证</h4>
                            <p>请先登录钉钉账户以使用智能总结功能</p>
                        </div>
                    </div>
                </div>

                <!-- 提示词模板选择 -->
                <section class="template-section" id="templateSection">
                    <label for="templateSelect" class="section-label">总结模板</label>
                    <select id="templateSelect" class="template-select">
                        <option value="default">默认总结</option>
                        <option value="news">新闻总结</option>
                        <option value="academic">学术文章</option>
                        <option value="technical">技术文档</option>
                    </select>
                </section>
            </div>

            <!-- 操作按钮区域 -->
            <section class="action-section">
                <button class="primary-btn" id="summarizeBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                        <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                        <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                    <span>总结当前页面</span>
                </button>

                <button class="secondary-btn" id="markdownBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2"/>
                        <path d="M7 15V9l2 2 2-2v6" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M17 11l-2 2 2 2" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                    <span>提取Markdown</span>
                </button>
            </section>

            <!-- 总结结果区域 -->
            <section class="result-section" id="resultSection" style="display: none;">
                <div class="result-header">
                    <h3 class="result-title">总结结果</h3>
                    <div class="result-actions">
                        <button class="icon-btn" id="popupBtn" title="在新窗口中查看">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" stroke="currentColor" stroke-width="2"/>
                                <polyline points="15,3 21,3 21,9" stroke="currentColor" stroke-width="2"/>
                                <line x1="10" y1="14" x2="21" y2="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <button class="icon-btn" id="copyBtn" title="复制到剪贴板">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <button class="icon-btn" id="exportBtn" title="导出为文件">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="result-content" id="resultContent">
                    <!-- 总结内容将在这里显示 -->
                </div>
                <div class="scroll-hint" id="scrollHint" style="display: none;">
                    <div class="scroll-hint-text">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M7 13l3 3 3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M7 6l3 3 3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        向下滚动查看更多内容
                    </div>
                </div>
                <div class="result-meta" id="resultMeta">
                    <!-- 元信息（使用的模板、生成时间等）将在这里显示 -->
                </div>
            </section>

            <!-- Markdown预览区域 -->
            <section class="markdown-section" id="markdownSection" style="display: none;">
                <div class="markdown-header">
                    <h3 class="markdown-title">Markdown预览</h3>
                    <div class="markdown-actions">
                        <button class="icon-btn" id="markdownPreviewBtn" title="独立窗口预览">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <button class="icon-btn" id="markdownCopyBtn" title="复制Markdown">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <button class="icon-btn" id="markdownDownloadBtn" title="下载Markdown文件">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <button class="icon-btn" id="markdownEditBtn" title="编辑Markdown">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 预览内容 -->
                <div class="markdown-content" id="markdownPreview">
                    <!-- Markdown渲染后的HTML将在这里显示 -->
                </div>

                <!-- 隐藏的源码编辑区域（保留用于兼容性） -->
                <div class="markdown-source" id="markdownSource" style="display: none;">
                    <textarea id="markdownTextarea" placeholder="Markdown内容将在这里显示..."></textarea>
                </div>

                <!-- 隐藏的标签切换区域（保留用于兼容性） -->
                <div class="markdown-tabs" style="display: none;">
                    <button class="tab-btn active" id="previewTab">预览</button>
                    <button class="tab-btn" id="sourceTab">源码</button>
                </div>

                <div class="markdown-meta" id="markdownMeta">
                    <!-- 元信息（生成时间、块数等）将在这里显示 -->
                </div>
            </section>

            <!-- 加载状态 -->
            <section class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <div class="loading-text">正在生成总结...</div>
                <div class="loading-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">0%</div>
                </div>
            </section>

            <!-- 错误提示 -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                        <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                        <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="error-message" id="errorMessage">出现错误</div>
                <button class="secondary-btn" id="retryBtn">重试</button>
            </section>
        </main>

        <!-- 底部信息 -->
        <footer class="sidebar-footer">
            <div class="footer-info">
                <span class="version">v1.0.0</span>
                <span class="divider">|</span>
                <span class="powered-by">Powered by 通义千问</span>
            </div>
        </footer>
    </div>

    <!-- Toast 通知 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <span class="toast-message" id="toastMessage"></span>
        </div>
    </div>

    <script src="../libs/markdown-parser.js"></script>
    <script src="../utils/ui-permission-manager.js"></script>
    <script src="sidebar.js"></script>
</body>
</html>
