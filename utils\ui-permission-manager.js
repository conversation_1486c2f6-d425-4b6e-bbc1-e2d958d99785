// UI权限控制管理器
// 根据钉钉认证状态动态控制UI元素的显示/隐藏和启用/禁用

class UIPermissionManager {
  constructor(options = {}) {
    // 配置选项
    this.debugMode = options.debugMode || false;

    // 权限级别定义
    this.PERMISSION_LEVELS = {
      PUBLIC: 'public',           // 公开访问，无需认证
      AUTHENTICATED: 'authenticated', // 需要认证
      PREMIUM: 'premium'          // 高级功能（预留）
    };

    // UI元素权限配置
    this.elementPermissions = {
      // 核心功能区域 - 需要认证
      'summarizeBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button',
        fallbackText: '请先登录钉钉账户'
      },
      'markdownBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button',
        fallbackText: '请先登录钉钉账户'
      },
      'templateSelect': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'select',
        fallbackText: '登录后可选择模板'
      },
      'templateSection': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'section'
      },

      // 认证遮罩层 - 与认证状态相反显示
      'authRequiredOverlay': {
        level: this.PERMISSION_LEVELS.PUBLIC,
        type: 'auth-overlay',
        showWhenUnauthenticated: true
      },

      // 功能容器
      'featureContainer': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'container'
      },
      
      // 结果和操作区域 - 需要认证
      'resultSection': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'section'
      },
      'copyBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button'
      },
      'exportBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button'
      },
      'retryBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button'
      },
      
      // Markdown相关功能 - 需要认证
      'markdownSection': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'section'
      },
      'markdownPreviewBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button'
      },
      'markdownCopyBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button'
      },
      'markdownDownloadBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button'
      },
      
      // 设置功能 - 需要认证
      'settingsBtn': {
        level: this.PERMISSION_LEVELS.AUTHENTICATED,
        type: 'button',
        fallbackText: '登录后可访问设置'
      },
      
      // 公开区域 - 无需认证
      'dingTalkAuthStatus': {
        level: this.PERMISSION_LEVELS.PUBLIC,
        type: 'section'
      },
      'pageInfo': {
        level: this.PERMISSION_LEVELS.PUBLIC,
        type: 'section'
      },
      'themeToggle': {
        level: this.PERMISSION_LEVELS.PUBLIC,
        type: 'button'
      }
    };

    // 当前认证状态
    this.isAuthenticated = false;
    this.userPermissionLevel = this.PERMISSION_LEVELS.PUBLIC;
    
    // 存储原始状态用于恢复
    this.originalStates = new Map();
    
    // 权限变化监听器
    this.permissionListeners = new Set();
  }

  // 设置认证状态
  setAuthenticationStatus(isAuthenticated, userInfo = null) {
    const wasAuthenticated = this.isAuthenticated;
    this.isAuthenticated = isAuthenticated;
    
    // 更新用户权限级别
    if (isAuthenticated) {
      this.userPermissionLevel = this.PERMISSION_LEVELS.AUTHENTICATED;
      // 如果有用户信息，可以根据用户类型设置更高权限
      if (userInfo && userInfo.isPremium) {
        this.userPermissionLevel = this.PERMISSION_LEVELS.PREMIUM;
      }
    } else {
      this.userPermissionLevel = this.PERMISSION_LEVELS.PUBLIC;
    }

    // 如果认证状态发生变化，更新UI权限
    if (wasAuthenticated !== isAuthenticated) {
      this.updateAllPermissions();
      this.notifyPermissionChange(isAuthenticated);
    }

    console.log(`权限状态更新: ${isAuthenticated ? '已认证' : '未认证'}, 权限级别: ${this.userPermissionLevel}`);
  }

  // 更新所有UI元素的权限
  updateAllPermissions() {
    Object.entries(this.elementPermissions).forEach(([elementId, config]) => {
      this.updateElementPermission(elementId, config);
    });
  }

  // 更新单个元素的权限
  updateElementPermission(elementId, config) {
    const element = document.getElementById(elementId);
    if (!element) {
      // 只在调试模式下显示警告，避免在正常使用中产生过多日志
      if (this.debugMode) {
        console.warn(`权限控制: 未找到元素 ${elementId}`);
      }
      return;
    }

    // 保存原始状态（仅在首次处理时）
    if (!this.originalStates.has(elementId)) {
      this.originalStates.set(elementId, {
        disabled: element.disabled,
        style: {
          display: element.style.display,
          opacity: element.style.opacity,
          pointerEvents: element.style.pointerEvents
        },
        title: element.title,
        innerHTML: element.innerHTML
      });
    }

    // 特殊处理认证遮罩层
    if (config.showWhenUnauthenticated) {
      if (this.isAuthenticated) {
        // 认证成功时隐藏遮罩层
        element.classList.add('fade-out');
        setTimeout(() => {
          element.style.display = 'none';
          element.classList.remove('fade-out');
        }, 300);
      } else {
        // 未认证时显示遮罩层
        element.style.display = 'flex';
        element.classList.add('fade-in');
        setTimeout(() => {
          element.classList.remove('fade-in');
        }, 300);
      }
      return;
    }

    const hasPermission = this.checkPermission(config.level);

    if (hasPermission) {
      this.enableElement(element, elementId, config);
    } else {
      this.disableElement(element, elementId, config);
    }
  }

  // 启用元素
  enableElement(element, elementId, config) {
    const originalState = this.originalStates.get(elementId);

    // 根据元素类型处理启用逻辑
    switch (config.type) {
      case 'container':
        // 容器类型：移除锁定状态，添加解锁动画
        element.classList.remove('auth-locked');
        element.classList.add('permission-unlocked');
        setTimeout(() => {
          element.classList.remove('permission-unlocked');
        }, 800);
        break;

      default:
        // 恢复原始状态
        element.disabled = originalState.disabled;
        element.style.opacity = originalState.style.opacity || '';
        element.style.pointerEvents = originalState.style.pointerEvents || '';
        element.title = originalState.title || '';

        // 移除权限相关的CSS类
        element.classList.remove('permission-disabled', 'auth-required');
        element.classList.add('permission-enabled');

        // 移除动画类
        setTimeout(() => {
          element.classList.remove('permission-enabled');
        }, 600);

        // 如果是section类型，确保显示
        if (config.type === 'section') {
          element.style.display = originalState.style.display || '';
        }
        break;
    }
  }

  // 禁用元素
  disableElement(element, elementId, config) {
    switch (config.type) {
      case 'container':
        // 容器类型：添加锁定状态
        element.classList.add('auth-locked');
        element.classList.remove('permission-unlocked');
        break;

      case 'button':
        element.disabled = true;
        element.style.opacity = '0.6';
        element.style.pointerEvents = 'none';
        element.title = config.fallbackText || '需要登录钉钉账户';
        element.classList.add('permission-disabled', 'auth-required');
        break;

      case 'select':
        element.disabled = true;
        element.style.opacity = '0.6';
        element.title = config.fallbackText || '需要登录钉钉账户';
        element.classList.add('permission-disabled', 'auth-required');
        break;

      case 'section':
        // 对于section，显示禁用状态
        element.style.opacity = '0.4';
        element.style.pointerEvents = 'none';
        element.classList.add('permission-disabled', 'auth-required');
        break;

      default:
        element.style.opacity = '0.6';
        element.style.pointerEvents = 'none';
        element.classList.add('permission-disabled', 'auth-required');
    }
  }

  // 检查权限
  checkPermission(requiredLevel) {
    const levelHierarchy = {
      [this.PERMISSION_LEVELS.PUBLIC]: 0,
      [this.PERMISSION_LEVELS.AUTHENTICATED]: 1,
      [this.PERMISSION_LEVELS.PREMIUM]: 2
    };

    const userLevel = levelHierarchy[this.userPermissionLevel] || 0;
    const required = levelHierarchy[requiredLevel] || 0;
    
    return userLevel >= required;
  }

  // 添加权限变化监听器
  addPermissionListener(callback) {
    this.permissionListeners.add(callback);
  }

  // 移除权限变化监听器
  removePermissionListener(callback) {
    this.permissionListeners.delete(callback);
  }

  // 通知权限变化
  notifyPermissionChange(isAuthenticated) {
    this.permissionListeners.forEach(callback => {
      try {
        callback(isAuthenticated, this.userPermissionLevel);
      } catch (error) {
        console.error('权限变化监听器执行失败:', error);
      }
    });
  }

  // 获取当前权限状态
  getPermissionStatus() {
    return {
      isAuthenticated: this.isAuthenticated,
      userPermissionLevel: this.userPermissionLevel,
      availableElements: Object.keys(this.elementPermissions).filter(elementId => {
        const config = this.elementPermissions[elementId];
        return this.checkPermission(config.level);
      })
    };
  }

  // 重置所有权限状态
  resetPermissions() {
    this.originalStates.forEach((originalState, elementId) => {
      const element = document.getElementById(elementId);
      if (element) {
        element.disabled = originalState.disabled;
        Object.assign(element.style, originalState.style);
        element.title = originalState.title;
        element.classList.remove('permission-disabled', 'auth-required');
      }
    });
    
    this.originalStates.clear();
    this.isAuthenticated = false;
    this.userPermissionLevel = this.PERMISSION_LEVELS.PUBLIC;
  }

  // 动态添加元素权限配置
  addElementPermission(elementId, config) {
    this.elementPermissions[elementId] = config;
    this.updateElementPermission(elementId, config);
  }

  // 移除元素权限配置
  removeElementPermission(elementId) {
    if (this.elementPermissions[elementId]) {
      // 恢复元素原始状态
      const element = document.getElementById(elementId);
      const originalState = this.originalStates.get(elementId);
      if (element && originalState) {
        element.disabled = originalState.disabled;
        Object.assign(element.style, originalState.style);
        element.title = originalState.title;
        element.classList.remove('permission-disabled', 'auth-required');
      }
      
      delete this.elementPermissions[elementId];
      this.originalStates.delete(elementId);
    }
  }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UIPermissionManager;
}
