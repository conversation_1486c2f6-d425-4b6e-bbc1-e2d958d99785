<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹出窗口功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-content {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
    </style>
</head>
<body>
    <h1>🧪 弹出窗口功能测试页面</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>这个页面用于测试Chrome插件的弹出窗口功能。请按照以下步骤进行测试：</p>
        <ol>
            <li>确保Chrome插件已正确安装并启用</li>
            <li>打开插件的侧边栏</li>
            <li>点击"总结当前页面"按钮生成总结</li>
            <li>在总结结果区域点击"在新窗口中查看"按钮</li>
            <li>验证弹出窗口是否正常显示</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>📝 测试内容</h2>
        <div class="test-content">
            <h3>智能网页总结助手功能测试</h3>
            <p>这是一个用于测试Chrome插件总结功能的示例页面。页面包含了丰富的文本内容，用于验证AI总结的准确性和弹出窗口的显示效果。</p>
            
            <h4>主要功能特性</h4>
            <ul>
                <li><strong>智能内容提取</strong>：自动识别并提取网页主要文本内容</li>
                <li><strong>AI智能总结</strong>：基于通义千问API生成高质量的内容摘要</li>
                <li><strong>弹出窗口显示</strong>：在独立窗口中展示总结结果</li>
                <li><strong>多种操作选项</strong>：复制、导出、打印等功能</li>
            </ul>

            <h4>技术实现</h4>
            <p>弹出窗口功能采用了以下技术方案：</p>
            <ul>
                <li>使用 <code>chrome.windows.create()</code> API创建新窗口</li>
                <li>通过Chrome本地存储传递数据</li>
                <li>响应式设计适配不同屏幕尺寸</li>
                <li>支持键盘快捷键操作</li>
            </ul>

            <h4>用户体验优化</h4>
            <p>为了提供更好的用户体验，弹出窗口具有以下特点：</p>
            <ol>
                <li><strong>清晰的界面设计</strong>：采用现代化的UI设计，提供良好的视觉体验</li>
                <li><strong>完整的功能支持</strong>：包含复制、导出、打印等所有必要功能</li>
                <li><strong>快捷键支持</strong>：支持常用的键盘快捷键操作</li>
                <li><strong>错误处理机制</strong>：完善的错误提示和重试机制</li>
            </ol>

            <blockquote>
                <p><em>注意：这个测试页面包含了足够的文本内容来验证总结功能的效果。在实际使用中，AI会根据页面内容生成相应的总结。</em></p>
            </blockquote>

            <h4>测试检查点</h4>
            <p>在测试过程中，请验证以下功能点：</p>
            <ul>
                <li>✅ 弹出窗口能够正常打开</li>
                <li>✅ 总结内容正确显示</li>
                <li>✅ 页面信息（标题、URL）正确显示</li>
                <li>✅ 复制功能正常工作</li>
                <li>✅ 导出功能正常工作</li>
                <li>✅ 打印功能正常工作</li>
                <li>✅ 窗口关闭功能正常</li>
                <li>✅ 快捷键支持正常</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 手动测试工具</h2>
        <p>以下按钮可以帮助您测试弹出窗口的各项功能：</p>
        
        <button class="test-button" onclick="testLocalStorage()">测试本地存储</button>
        <button class="test-button" onclick="testWindowAPI()">测试窗口API</button>
        <button class="test-button" onclick="generateTestData()">生成测试数据</button>
        <button class="test-button" onclick="clearTestData()">清理测试数据</button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📊 测试结果</h2>
        <div id="testStatus" class="status info">
            等待测试开始...
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function showResults(message) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML += `<div class="status info">${message}</div>`;
        }

        function testLocalStorage() {
            try {
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('test_popup', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('test_popup'));
                
                if (retrieved && retrieved.test === 'data') {
                    showResults('✅ 本地存储测试通过');
                    showStatus('本地存储功能正常', 'success');
                } else {
                    showResults('❌ 本地存储测试失败');
                    showStatus('本地存储功能异常', 'error');
                }
            } catch (error) {
                showResults('❌ 本地存储测试出错: ' + error.message);
                showStatus('本地存储测试出错', 'error');
            }
        }

        function testWindowAPI() {
            try {
                if (typeof window !== 'undefined' && window.open) {
                    showResults('✅ 窗口API可用');
                    showStatus('窗口API功能正常', 'success');
                } else {
                    showResults('❌ 窗口API不可用');
                    showStatus('窗口API功能异常', 'error');
                }
            } catch (error) {
                showResults('❌ 窗口API测试出错: ' + error.message);
                showStatus('窗口API测试出错', 'error');
            }
        }

        function generateTestData() {
            const testSummary = {
                dataId: Date.now().toString(),
                summary: '这是一个测试总结内容。包含了多个段落和格式化文本。\n\n## 主要内容\n\n1. 测试功能验证\n2. 弹出窗口显示\n3. 用户交互测试\n\n**重要提示**：这是测试数据，用于验证弹出窗口功能。',
                template: '测试模板',
                title: '弹出窗口功能测试页面',
                url: window.location.href,
                timestamp: Date.now()
            };

            localStorage.setItem(`summary_${testSummary.dataId}`, JSON.stringify(testSummary));
            showResults(`✅ 测试数据已生成，ID: ${testSummary.dataId}`);
            showStatus('测试数据生成成功', 'success');
        }

        function clearTestData() {
            try {
                // 清理所有测试相关的数据
                for (let i = localStorage.length - 1; i >= 0; i--) {
                    const key = localStorage.key(i);
                    if (key && (key.startsWith('summary_') || key.startsWith('test_'))) {
                        localStorage.removeItem(key);
                    }
                }
                showResults('✅ 测试数据已清理');
                showStatus('测试数据清理完成', 'success');
            } catch (error) {
                showResults('❌ 清理测试数据出错: ' + error.message);
                showStatus('清理测试数据出错', 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('测试页面已加载，请开始测试', 'info');
        });
    </script>
</body>
</html>
