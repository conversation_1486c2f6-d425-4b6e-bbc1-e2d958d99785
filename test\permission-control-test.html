<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限控制系统测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-btn.primary {
            background: #007acc;
            color: white;
        }
        
        .test-btn.secondary {
            background: #6c757d;
            color: white;
        }
        
        .test-btn.success {
            background: #28a745;
            color: white;
        }
        
        .test-btn.danger {
            background: #dc3545;
            color: white;
        }
        
        .test-btn:hover {
            opacity: 0.8;
        }
        
        .status-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007acc;
        }
        
        .permission-demo {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .demo-section {
            margin-bottom: 15px;
        }
        
        .demo-section h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .demo-button {
            padding: 10px 20px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
        }
        
        .demo-section-area {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 5px;
            border: 1px solid #ddd;
        }
        
        .log-area {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .auth-hint {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            text-align: center;
            font-size: 14px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .auth-hint .auth-hint-icon {
            font-size: 18px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔐 Chrome插件权限控制系统测试</h1>
            <p>测试基于钉钉认证的UI权限控制功能</p>
        </div>
        
        <div class="test-controls">
            <button class="test-btn primary" onclick="simulateLogin()">模拟登录</button>
            <button class="test-btn secondary" onclick="simulateLogout()">模拟登出</button>
            <button class="test-btn success" onclick="testPermissions()">测试权限</button>
            <button class="test-btn danger" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="status-display">
            <strong>当前状态：</strong>
            <span id="authStatus">未认证</span> |
            <strong>权限级别：</strong>
            <span id="permissionLevel">public</span>
        </div>
    </div>
    
    <div class="test-container">
        <h3>权限控制演示区域</h3>
        
        <!-- 认证提示区域 -->
        <div class="auth-hint" id="authHint">
            <span class="auth-hint-icon">🔒</span>
            请先登录钉钉账户以使用智能总结功能
        </div>
        
        <!-- 模板选择区域 -->
        <div class="demo-section" id="templateSection">
            <h4>总结模板选择</h4>
            <select id="templateSelect" class="demo-select">
                <option value="default">默认总结</option>
                <option value="news">新闻总结</option>
                <option value="academic">学术文章</option>
                <option value="technical">技术文档</option>
            </select>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="demo-section">
            <h4>核心功能按钮</h4>
            <button class="demo-button" id="summarizeBtn">总结当前页面</button>
            <button class="demo-button" id="markdownBtn">提取Markdown</button>
        </div>
        
        <!-- 结果区域 -->
        <div class="demo-section-area" id="resultSection">
            <h4>结果显示区域</h4>
            <p>这里会显示总结结果...</p>
            <button class="demo-button" id="copyBtn">复制结果</button>
            <button class="demo-button" id="exportBtn">导出文件</button>
        </div>
        
        <!-- 设置按钮 -->
        <div class="demo-section">
            <h4>设置功能</h4>
            <button class="demo-button" id="settingsBtn">打开设置</button>
        </div>
        
        <!-- 公开区域 -->
        <div class="demo-section-area" id="pageInfo">
            <h4>页面信息（公开访问）</h4>
            <p>当前页面信息始终可见</p>
            <button class="demo-button" id="themeToggle">切换主题</button>
        </div>
    </div>
    
    <div class="test-container">
        <h3>测试日志</h3>
        <div class="log-area" id="logArea"></div>
    </div>

    <!-- 引入权限控制管理器 -->
    <script src="../utils/ui-permission-manager.js"></script>
    
    <script>
        // 初始化权限管理器
        const permissionManager = new UIPermissionManager();
        
        // 添加权限变化监听器
        permissionManager.addPermissionListener((isAuthenticated, permissionLevel) => {
            updateStatusDisplay(isAuthenticated, permissionLevel);
            log(`权限状态变化: ${isAuthenticated ? '已认证' : '未认证'}, 级别: ${permissionLevel}`);
        });
        
        // 初始化权限状态
        permissionManager.updateAllPermissions();
        updateStatusDisplay(false, 'public');
        
        // 模拟登录
        function simulateLogin() {
            const userInfo = {
                name: '测试用户',
                avatar: null,
                isPremium: false
            };
            
            permissionManager.setAuthenticationStatus(true, userInfo);
            log('模拟登录成功');
        }
        
        // 模拟登出
        function simulateLogout() {
            permissionManager.setAuthenticationStatus(false);
            log('模拟登出成功');
        }
        
        // 测试权限
        function testPermissions() {
            const status = permissionManager.getPermissionStatus();
            log('当前权限状态:');
            log(`- 认证状态: ${status.isAuthenticated}`);
            log(`- 权限级别: ${status.userPermissionLevel}`);
            log(`- 可用元素: ${status.availableElements.join(', ')}`);
        }
        
        // 更新状态显示
        function updateStatusDisplay(isAuthenticated, permissionLevel) {
            document.getElementById('authStatus').textContent = isAuthenticated ? '已认证' : '未认证';
            document.getElementById('permissionLevel').textContent = permissionLevel;
        }
        
        // 日志功能
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('logArea').textContent = '';
        }
        
        // 初始化日志
        log('权限控制系统测试页面已加载');
        log('点击"模拟登录"或"模拟登出"来测试权限控制');
    </script>
</body>
</html>
