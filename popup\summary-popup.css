/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8f9fa;
    overflow-x: hidden;
}

/* 主容器 */
.popup-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.popup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
}

.popup-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.icon-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.close-btn:hover {
    background: #ff4757;
}

/* 页面信息区域 */
.page-info {
    padding: 16px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.page-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    word-break: break-word;
}

.page-url {
    font-size: 14px;
    color: #6c757d;
    word-break: break-all;
}

/* 主要内容区域 */
.popup-main {
    flex: 1;
    padding: 24px;
    min-height: 400px;
    position: relative;
}

/* 加载状态 */
.loading-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    gap: 16px;
}

.loading-spinner {
    width: 48px;
    height: 48px;
}

.spinner {
    width: 100%;
    height: 100%;
    border: 4px solid #e9ecef;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 16px;
    color: #6c757d;
}

/* 错误状态 */
.error-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    gap: 16px;
}

.error-icon {
    color: #dc3545;
}

.error-message {
    font-size: 16px;
    color: #dc3545;
    text-align: center;
}

.retry-btn {
    padding: 8px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s ease;
}

.retry-btn:hover {
    background: #5a6fd8;
}

/* 内容区域 */
.content-section {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.summary-content {
    line-height: 1.8;
    font-size: 16px;
    color: #2c3e50;
    max-width: none;
}

/* 总结内容样式 */
.summary-content h1,
.summary-content h2,
.summary-content h3,
.summary-content h4,
.summary-content h5,
.summary-content h6 {
    margin: 24px 0 16px 0;
    color: #2c3e50;
    font-weight: 600;
}

.summary-content h1 { font-size: 24px; }
.summary-content h2 { font-size: 20px; }
.summary-content h3 { font-size: 18px; }

.summary-content p {
    margin: 16px 0;
    text-align: justify;
}

.summary-content ul,
.summary-content ol {
    margin: 16px 0;
    padding-left: 24px;
}

.summary-content li {
    margin: 8px 0;
}

.summary-content blockquote {
    margin: 16px 0;
    padding: 16px 20px;
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    border-radius: 0 6px 6px 0;
}

.summary-content code {
    background: #f1f3f4;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
}

.summary-content pre {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 16px 0;
}

/* 底部区域 */
.popup-footer {
    padding: 16px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.meta-info {
    font-size: 14px;
    color: #6c757d;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.meta-info span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.footer-actions {
    display: flex;
    gap: 8px;
}

.secondary-btn {
    padding: 8px 16px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s ease;
}

.secondary-btn:hover {
    background: #5a6268;
}

/* Toast 通知 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: #dc3545;
}

.toast.warning {
    background: #ffc107;
    color: #212529;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .popup-container {
        margin: 0;
        min-height: 100vh;
    }
    
    .popup-main {
        padding: 16px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
    
    .popup-footer {
        flex-direction: column;
        align-items: stretch;
    }
    
    .meta-info {
        justify-content: center;
    }
}

/* 打印样式 */
@media print {
    .popup-header,
    .popup-footer {
        display: none;
    }
    
    .popup-container {
        box-shadow: none;
        background: white;
    }
    
    .popup-main {
        padding: 0;
    }
    
    .summary-content {
        font-size: 14px;
        line-height: 1.6;
    }
}
