<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总结结果 - 智能网页总结助手</title>
    <link rel="stylesheet" href="summary-popup.css">
</head>
<body>
    <div class="popup-container">
        <!-- 头部区域 -->
        <header class="popup-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h1 class="popup-title">总结结果</h1>
                </div>
                <div class="header-actions">
                    <button class="icon-btn" id="copyBtn" title="复制到剪贴板">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                    <button class="icon-btn" id="exportBtn" title="导出为文件">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                    <button class="icon-btn" id="printBtn" title="打印">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                            <polyline points="6,9 6,2 18,2 18,9" stroke="currentColor" stroke-width="2"/>
                            <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2" stroke="currentColor" stroke-width="2"/>
                            <rect x="6" y="14" width="12" height="8" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                    <button class="icon-btn close-btn" id="closeBtn" title="关闭窗口">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                            <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                            <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- 页面信息区域 -->
        <section class="page-info">
            <div class="page-title" id="pageTitle">页面标题</div>
            <div class="page-url" id="pageUrl">页面URL</div>
        </section>

        <!-- 主要内容区域 -->
        <main class="popup-main">
            <!-- 加载状态 -->
            <div class="loading-section" id="loadingSection">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <div class="loading-text">正在加载总结结果...</div>
            </div>

            <!-- 错误状态 -->
            <div class="error-section" id="errorSection" style="display: none;">
                <div class="error-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                        <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                        <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="error-message" id="errorMessage">加载失败</div>
                <button class="retry-btn" id="retryBtn">重试</button>
            </div>

            <!-- 总结内容区域 -->
            <div class="content-section" id="contentSection" style="display: none;">
                <div class="summary-content" id="summaryContent">
                    <!-- 总结内容将在这里显示 -->
                </div>
            </div>
        </main>

        <!-- 底部信息区域 -->
        <footer class="popup-footer">
            <div class="meta-info" id="metaInfo">
                <!-- 元信息（模板、生成时间等）将在这里显示 -->
            </div>
            <div class="footer-actions">
                <button class="secondary-btn" id="backToSidebarBtn">返回侧边栏</button>
            </div>
        </footer>
    </div>

    <!-- Toast 通知 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <span class="toast-message" id="toastMessage"></span>
        </div>
    </div>

    <script src="summary-popup.js"></script>
</body>
</html>
