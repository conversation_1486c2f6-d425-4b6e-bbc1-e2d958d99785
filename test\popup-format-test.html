<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup格式化测试</title>
    <style>
        body {
            margin: 20px;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #007acc;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a9e;
        }
        textarea {
            width: 100%;
            height: 150px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .output {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: #fafafa;
            min-height: 100px;
        }
        .markdown-table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        .markdown-table th,
        .markdown-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .markdown-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        blockquote {
            border-left: 4px solid #007acc;
            margin: 10px 0;
            padding: 10px 20px;
            background: #f9f9f9;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 2px;
            font-family: 'Courier New', monospace;
        }
        pre code {
            background: none;
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Popup格式化功能测试</h1>
        
        <div class="test-section">
            <h3>测试Markdown格式化</h3>
            <div class="controls">
                <button onclick="loadSampleMarkdown()">加载示例Markdown</button>
                <button onclick="formatContent()">格式化</button>
                <button onclick="testCopy()">测试复制</button>
            </div>
            <textarea id="markdownInput" placeholder="输入Markdown内容..."></textarea>
            <div class="output" id="formattedOutput"></div>
        </div>

        <div class="test-section">
            <h3>模拟总结数据</h3>
            <div class="controls">
                <button onclick="simulatePopupData()">模拟Popup数据</button>
            </div>
            <div id="popupSimulation"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../libs/markdown-parser.js"></script>
    <script>
        // 模拟SummaryPopup类的formatSummary方法
        class TestSummaryPopup {
            constructor() {
                this.markdownParser = new MarkdownParser();
            }

            formatSummary(summary) {
                if (!summary || typeof summary !== 'string') {
                    return '<p>暂无内容</p>';
                }

                try {
                    // 解析Markdown为HTML
                    const html = this.markdownParser.parse(summary);
                    return html || '<p>暂无内容</p>';
                } catch (error) {
                    console.error('Markdown解析失败:', error);
                    // 降级到简单的文本显示
                    return `<p>${summary.replace(/\n/g, '<br>')}</p>`;
                }
            }

            async copyToClipboard(summaryData) {
                try {
                    // 优先复制原始的Markdown文本
                    const markdownContent = summaryData.summary || '';
                    
                    // 添加页面信息到复制内容
                    const pageInfo = `# ${summaryData.title || '页面总结'}

> **来源：** ${summaryData.url || ''}  
> **生成时间：** ${new Date(summaryData.timestamp || Date.now()).toLocaleString('zh-CN')}  
> **模板：** ${summaryData.template || '默认模板'}

---

`;
                    
                    const fullContent = pageInfo + markdownContent;
                    await navigator.clipboard.writeText(fullContent);
                    alert('Markdown内容已复制到剪贴板');
                } catch (error) {
                    console.error('复制失败:', error);
                    alert('复制失败，请手动选择文本复制');
                }
            }
        }

        const testPopup = new TestSummaryPopup();
        const markdownInput = document.getElementById('markdownInput');
        const formattedOutput = document.getElementById('formattedOutput');
        const popupSimulation = document.getElementById('popupSimulation');

        function loadSampleMarkdown() {
            const sampleMarkdown = `# 技术文档总结

## 主要功能特性

这个Chrome插件提供了以下**核心功能**：

- **智能总结**：使用AI技术自动提取页面关键信息
- **Markdown支持**：完整支持Markdown格式显示
- **多种模板**：支持新闻、学术、技术等不同类型的总结模板
- **一键复制**：保持原始Markdown格式的复制功能

## 使用方法

1. 安装Chrome插件
2. 打开需要总结的网页
3. 点击插件图标
4. 选择合适的总结模板
5. 点击"总结当前页面"

## 注意事项

> **重要提醒**：请确保网页内容已完全加载后再进行总结。

### 代码示例

\`\`\`javascript
// 格式化总结内容
formatSummary(summary) {
    return this.markdownParser.parse(summary);
}
\`\`\`

## 适用场景

| 场景 | 适用性 | 说明 |
|------|--------|------|
| 新闻文章 | ⭐⭐⭐⭐⭐ | 快速提取新闻要点 |
| 技术文档 | ⭐⭐⭐⭐⭐ | 总结技术要点和使用方法 |
| 学术论文 | ⭐⭐⭐⭐ | 提取研究方法和结论 |
| 博客文章 | ⭐⭐⭐ | 概括文章主要观点 |

---

*测试时间：${new Date().toLocaleString('zh-CN')}*`;

            markdownInput.value = sampleMarkdown;
        }

        function formatContent() {
            const markdown = markdownInput.value;
            const formatted = testPopup.formatSummary(markdown);
            formattedOutput.innerHTML = formatted;
        }

        function testCopy() {
            const mockData = {
                summary: markdownInput.value,
                title: '测试页面标题',
                url: 'https://example.com/test',
                template: '技术文档',
                timestamp: Date.now()
            };
            testPopup.copyToClipboard(mockData);
        }

        function simulatePopupData() {
            const mockSummaryData = {
                summary: markdownInput.value || '# 默认总结\n\n这是一个测试总结内容。',
                title: '测试网页标题',
                url: 'https://example.com/test-page',
                template: '默认模板',
                timestamp: Date.now()
            };

            const formatted = testPopup.formatSummary(mockSummaryData.summary);
            
            popupSimulation.innerHTML = `
                <div style="border: 1px solid #ddd; padding: 15px; border-radius: 4px;">
                    <h4>页面信息</h4>
                    <p><strong>标题：</strong>${mockSummaryData.title}</p>
                    <p><strong>URL：</strong>${mockSummaryData.url}</p>
                    <p><strong>模板：</strong>${mockSummaryData.template}</p>
                    <p><strong>生成时间：</strong>${new Date(mockSummaryData.timestamp).toLocaleString('zh-CN')}</p>
                    
                    <h4>格式化后的内容</h4>
                    <div style="border: 1px solid #eee; padding: 10px; background: white;">
                        ${formatted}
                    </div>
                    
                    <button onclick="testCopyWithData()" style="margin-top: 10px;">复制完整内容</button>
                </div>
            `;
        }

        function testCopyWithData() {
            const mockData = {
                summary: markdownInput.value,
                title: '测试网页标题',
                url: 'https://example.com/test-page',
                template: '默认模板',
                timestamp: Date.now()
            };
            testPopup.copyToClipboard(mockData);
        }

        // 初始化
        loadSampleMarkdown();
        formatContent();
    </script>
</body>
</html>
