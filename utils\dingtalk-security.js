// 钉钉认证安全管理器
// 处理钉钉认证过程中的安全策略和风险控制

class DingTalkSecurityManager {
  constructor() {
    this.securityConfig = {
      // Cookie安全配置
      cookieSecurity: {
        requireSecure: true,
        requireHttpOnly: false, // 钉钉Cookie可能不是HttpOnly
        maxAge: 24 * 60 * 60 * 1000, // 24小时
        domainWhitelist: ['.dingtalk.com']
      },
      
      // API安全配置
      apiSecurity: {
        allowedDomains: [
          'docs.dingtalk.com',
          'pre-docs.dingtalk.com',
          'oapi.dingtalk.com',
          'api.dingtalk.com'
        ],
        requireHttps: true,
        maxRetries: 3,
        timeout: 30000, // 30秒
        rateLimitWindow: 60000, // 1分钟
        maxRequestsPerWindow: 60
      },
      
      // 认证安全配置
      authSecurity: {
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15分钟
        sessionTimeout: 24 * 60 * 60 * 1000, // 24小时
        requireReauth: false
      }
    };
    
    this.requestHistory = new Map(); // 请求历史记录
    this.loginAttempts = new Map(); // 登录尝试记录
    this.securityEvents = []; // 安全事件记录
  }

  // 验证Cookie安全性
  validateCookieSecurity(cookie) {
    const issues = [];
    
    // 检查域名
    if (!this.securityConfig.cookieSecurity.domainWhitelist.some(domain => 
      cookie.domain === domain || cookie.domain.endsWith(domain))) {
      issues.push({
        level: 'error',
        message: `Cookie域名不在白名单中: ${cookie.domain}`
      });
    }
    
    // 检查HTTPS
    if (this.securityConfig.cookieSecurity.requireSecure && !cookie.secure) {
      issues.push({
        level: 'warning',
        message: 'Cookie未设置Secure标志，存在安全风险'
      });
    }
    
    // 检查过期时间
    if (cookie.expirationDate) {
      const expirationTime = cookie.expirationDate * 1000;
      const now = Date.now();
      const maxAge = this.securityConfig.cookieSecurity.maxAge;
      
      if (expirationTime - now > maxAge) {
        issues.push({
          level: 'warning',
          message: `Cookie过期时间过长，可能存在安全风险`
        });
      }
      
      if (expirationTime <= now) {
        issues.push({
          level: 'error',
          message: 'Cookie已过期'
        });
      }
    }
    
    // 检查Cookie值
    if (!cookie.value || cookie.value.length < 10) {
      issues.push({
        level: 'error',
        message: 'Cookie值无效或过短'
      });
    }
    
    return {
      isValid: issues.filter(issue => issue.level === 'error').length === 0,
      issues: issues
    };
  }

  // 验证API请求安全性
  validateApiSecurity(url, options = {}) {
    const issues = [];
    
    try {
      const urlObj = new URL(url);
      
      // 检查协议
      if (this.securityConfig.apiSecurity.requireHttps && urlObj.protocol !== 'https:') {
        issues.push({
          level: 'error',
          message: 'API请求必须使用HTTPS协议'
        });
      }
      
      // 检查域名
      if (!this.securityConfig.apiSecurity.allowedDomains.includes(urlObj.hostname)) {
        issues.push({
          level: 'error',
          message: `API域名不在允许列表中: ${urlObj.hostname}`
        });
      }
      
      // 检查路径安全性
      if (urlObj.pathname.includes('..') || 
          urlObj.pathname.includes('<') || 
          urlObj.pathname.includes('>') ||
          urlObj.pathname.includes('script')) {
        issues.push({
          level: 'error',
          message: 'API路径包含可疑字符'
        });
      }
      
      // 检查请求频率
      const rateLimitResult = this.checkRateLimit(urlObj.hostname);
      if (!rateLimitResult.allowed) {
        issues.push({
          level: 'error',
          message: `API请求频率过高，请稍后重试 (${rateLimitResult.remainingTime}ms)`
        });
      }
      
    } catch (error) {
      issues.push({
        level: 'error',
        message: `URL解析失败: ${error.message}`
      });
    }
    
    return {
      isValid: issues.filter(issue => issue.level === 'error').length === 0,
      issues: issues
    };
  }

  // 检查请求频率限制
  checkRateLimit(hostname) {
    const now = Date.now();
    const windowSize = this.securityConfig.apiSecurity.rateLimitWindow;
    const maxRequests = this.securityConfig.apiSecurity.maxRequestsPerWindow;
    
    if (!this.requestHistory.has(hostname)) {
      this.requestHistory.set(hostname, []);
    }
    
    const requests = this.requestHistory.get(hostname);
    
    // 清理过期的请求记录
    const validRequests = requests.filter(timestamp => now - timestamp < windowSize);
    this.requestHistory.set(hostname, validRequests);
    
    if (validRequests.length >= maxRequests) {
      const oldestRequest = Math.min(...validRequests);
      const remainingTime = windowSize - (now - oldestRequest);
      
      return {
        allowed: false,
        remainingTime: remainingTime
      };
    }
    
    // 记录当前请求
    validRequests.push(now);
    this.requestHistory.set(hostname, validRequests);
    
    return {
      allowed: true,
      remainingRequests: maxRequests - validRequests.length
    };
  }

  // 记录登录尝试
  recordLoginAttempt(success, userAgent = '', ipAddress = '') {
    const now = Date.now();
    const key = `${userAgent}_${ipAddress}`;
    
    if (!this.loginAttempts.has(key)) {
      this.loginAttempts.set(key, {
        attempts: 0,
        lastAttempt: 0,
        locked: false,
        lockExpiry: 0
      });
    }
    
    const record = this.loginAttempts.get(key);
    
    if (success) {
      // 登录成功，重置计数
      record.attempts = 0;
      record.locked = false;
      record.lockExpiry = 0;
    } else {
      // 登录失败，增加计数
      record.attempts++;
      record.lastAttempt = now;
      
      // 检查是否需要锁定
      if (record.attempts >= this.securityConfig.authSecurity.maxLoginAttempts) {
        record.locked = true;
        record.lockExpiry = now + this.securityConfig.authSecurity.lockoutDuration;
        
        this.recordSecurityEvent('login_lockout', {
          key: key,
          attempts: record.attempts,
          lockExpiry: record.lockExpiry
        });
      }
    }
    
    this.loginAttempts.set(key, record);
    
    return {
      isLocked: record.locked && now < record.lockExpiry,
      remainingAttempts: Math.max(0, this.securityConfig.authSecurity.maxLoginAttempts - record.attempts),
      lockExpiry: record.lockExpiry
    };
  }

  // 检查是否被锁定
  isLoginLocked(userAgent = '', ipAddress = '') {
    const key = `${userAgent}_${ipAddress}`;
    const record = this.loginAttempts.get(key);
    
    if (!record) {
      return { isLocked: false };
    }
    
    const now = Date.now();
    
    if (record.locked && now >= record.lockExpiry) {
      // 锁定已过期，重置状态
      record.locked = false;
      record.attempts = 0;
      record.lockExpiry = 0;
      this.loginAttempts.set(key, record);
    }
    
    return {
      isLocked: record.locked && now < record.lockExpiry,
      remainingTime: record.locked ? Math.max(0, record.lockExpiry - now) : 0,
      attempts: record.attempts
    };
  }

  // 记录安全事件
  recordSecurityEvent(eventType, details = {}) {
    const event = {
      type: eventType,
      timestamp: Date.now(),
      details: details,
      userAgent: navigator.userAgent || 'unknown'
    };
    
    this.securityEvents.push(event);
    
    // 限制事件记录数量
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-500);
    }
    
    console.log('安全事件记录:', event);
    
    // 对于严重事件，可以考虑上报或告警
    if (['login_lockout', 'suspicious_request', 'cookie_tampering'].includes(eventType)) {
      console.warn('检测到安全风险事件:', event);
    }
  }

  // 获取安全报告
  getSecurityReport() {
    const now = Date.now();
    const last24Hours = now - 24 * 60 * 60 * 1000;
    
    const recentEvents = this.securityEvents.filter(event => event.timestamp > last24Hours);
    
    const eventsByType = {};
    recentEvents.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
    });
    
    const activeRateLimits = Array.from(this.requestHistory.entries())
      .filter(([hostname, requests]) => requests.length > 0)
      .map(([hostname, requests]) => ({
        hostname,
        requestCount: requests.length,
        lastRequest: Math.max(...requests)
      }));
    
    const activeLockouts = Array.from(this.loginAttempts.entries())
      .filter(([key, record]) => record.locked && now < record.lockExpiry)
      .map(([key, record]) => ({
        key,
        attempts: record.attempts,
        lockExpiry: record.lockExpiry,
        remainingTime: record.lockExpiry - now
      }));
    
    return {
      timestamp: now,
      period: '24小时',
      totalEvents: recentEvents.length,
      eventsByType: eventsByType,
      activeRateLimits: activeRateLimits,
      activeLockouts: activeLockouts,
      securityScore: this.calculateSecurityScore(recentEvents, activeLockouts)
    };
  }

  // 计算安全评分
  calculateSecurityScore(recentEvents, activeLockouts) {
    let score = 100;
    
    // 根据安全事件扣分
    const severityScores = {
      'login_lockout': -10,
      'suspicious_request': -5,
      'cookie_tampering': -15,
      'rate_limit_exceeded': -3,
      'invalid_domain': -8
    };
    
    recentEvents.forEach(event => {
      const penalty = severityScores[event.type] || -1;
      score += penalty;
    });
    
    // 根据活跃锁定扣分
    score -= activeLockouts.length * 5;
    
    return Math.max(0, Math.min(100, score));
  }

  // 清理过期数据
  cleanup() {
    const now = Date.now();
    const windowSize = this.securityConfig.apiSecurity.rateLimitWindow;
    
    // 清理请求历史
    for (const [hostname, requests] of this.requestHistory.entries()) {
      const validRequests = requests.filter(timestamp => now - timestamp < windowSize);
      if (validRequests.length === 0) {
        this.requestHistory.delete(hostname);
      } else {
        this.requestHistory.set(hostname, validRequests);
      }
    }
    
    // 清理过期的登录锁定
    for (const [key, record] of this.loginAttempts.entries()) {
      if (record.locked && now >= record.lockExpiry) {
        record.locked = false;
        record.attempts = 0;
        record.lockExpiry = 0;
        this.loginAttempts.set(key, record);
      }
    }
    
    // 清理旧的安全事件（保留最近7天）
    const sevenDaysAgo = now - 7 * 24 * 60 * 60 * 1000;
    this.securityEvents = this.securityEvents.filter(event => event.timestamp > sevenDaysAgo);
  }
}

// 导出
if (typeof window !== 'undefined') {
  window.DingTalkSecurityManager = DingTalkSecurityManager;
}
