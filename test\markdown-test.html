<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown解析测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: 80vh;
        }
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .panel h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        #markdownInput {
            width: 100%;
            height: calc(100% - 100px);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: none;
        }
        #htmlOutput {
            height: calc(100% - 100px);
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background: #fafafa;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a9e;
        }
        .markdown-table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        .markdown-table th,
        .markdown-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .markdown-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        blockquote {
            border-left: 4px solid #007acc;
            margin: 10px 0;
            padding: 10px 20px;
            background: #f9f9f9;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 2px;
            font-family: 'Courier New', monospace;
        }
        pre code {
            background: none;
            padding: 0;
        }
    </style>
</head>
<body>
    <h1>Markdown解析器测试</h1>
    
    <div class="container">
        <div class="panel">
            <h2>Markdown输入</h2>
            <div class="controls">
                <button onclick="loadSampleContent()">加载示例内容</button>
                <button onclick="clearContent()">清空</button>
                <button onclick="copyMarkdown()">复制Markdown</button>
            </div>
            <textarea id="markdownInput" placeholder="在这里输入Markdown内容..."></textarea>
        </div>
        
        <div class="panel">
            <h2>HTML输出</h2>
            <div class="controls">
                <button onclick="parseMarkdown()">解析</button>
                <button onclick="copyHtml()">复制HTML</button>
            </div>
            <div id="htmlOutput"></div>
        </div>
    </div>

    <script src="../libs/markdown-parser.js"></script>
    <script>
        const markdownInput = document.getElementById('markdownInput');
        const htmlOutput = document.getElementById('htmlOutput');
        const parser = new MarkdownParser();

        // 自动解析
        markdownInput.addEventListener('input', parseMarkdown);

        function parseMarkdown() {
            const markdown = markdownInput.value;
            const html = parser.parse(markdown);
            htmlOutput.innerHTML = html;
        }

        function loadSampleContent() {
            const sampleMarkdown = `# 网页总结示例

## 核心内容

这是一个**重要的**总结，包含了*关键信息*。

### 主要特点

- 支持**粗体**和*斜体*文本
- 支持\`行内代码\`
- 支持链接：[GitHub](https://github.com)
- 支持~~删除线~~文本

### 代码示例

\`\`\`javascript
function summarize(content) {
    return parser.parse(content);
}
\`\`\`

### 表格示例

| 功能 | 状态 | 说明 |
|------|------|------|
| 标题解析 | ✅ | 支持1-6级标题 |
| 列表解析 | ✅ | 支持有序和无序列表 |
| 表格解析 | ✅ | 支持基本表格格式 |
| 代码块 | ✅ | 支持语法高亮 |

### 引用内容

> 这是一个重要的引用内容，
> 可以跨越多行。

### 总结

1. 格式化功能完整
2. 支持常用Markdown语法
3. 适合AI生成的内容展示

---

*生成时间：${new Date().toLocaleString('zh-CN')}*`;

            markdownInput.value = sampleMarkdown;
            parseMarkdown();
        }

        function clearContent() {
            markdownInput.value = '';
            htmlOutput.innerHTML = '';
        }

        async function copyMarkdown() {
            try {
                await navigator.clipboard.writeText(markdownInput.value);
                alert('Markdown内容已复制到剪贴板');
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败');
            }
        }

        async function copyHtml() {
            try {
                await navigator.clipboard.writeText(htmlOutput.innerHTML);
                alert('HTML内容已复制到剪贴板');
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败');
            }
        }

        // 初始加载示例内容
        loadSampleContent();
    </script>
</body>
</html>
