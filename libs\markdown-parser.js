// 增强的Markdown解析器
// 用于在Chrome扩展中渲染Markdown内容，支持完整的标准语法

class MarkdownParser {
  constructor() {
    // 预处理规则（在主要规则之前执行）
    this.preRules = [
      // 转义字符处理
      { pattern: /\\([\\`*_{}[\]()#+\-.!])/g, replacement: '&#92;$1' },
    ];

    // 主要解析规则
    this.rules = [
      // 标题（支持1-6级，包括带#的格式）
      { pattern: /^#{6}\s+(.*$)/gm, replacement: '<h6>$1</h6>' },
      { pattern: /^#{5}\s+(.*$)/gm, replacement: '<h5>$1</h5>' },
      { pattern: /^#{4}\s+(.*$)/gm, replacement: '<h4>$1</h4>' },
      { pattern: /^#{3}\s+(.*$)/gm, replacement: '<h3>$1</h3>' },
      { pattern: /^#{2}\s+(.*$)/gm, replacement: '<h2>$1</h2>' },
      { pattern: /^#{1}\s+(.*$)/gm, replacement: '<h1>$1</h1>' },

      // 代码块（支持语言标识）
      { pattern: /```(\w+)?\n([\s\S]*?)```/g, replacement: '<pre><code class="language-$1">$2</code></pre>' },
      { pattern: /```([\s\S]*?)```/g, replacement: '<pre><code>$1</code></pre>' },

      // 行内代码
      { pattern: /`([^`\n]+)`/g, replacement: '<code>$1</code>' },

      // 图片
      { pattern: /!\[([^\]]*)\]\(([^)]+)\)/g, replacement: '<img src="$2" alt="$1" style="max-width: 100%; height: auto;" />' },

      // 链接
      { pattern: /\[([^\]]+)\]\(([^)]+)\)/g, replacement: '<a href="$2" target="_blank">$1</a>' },

      // 粗体和斜体（改进顺序，避免冲突）
      { pattern: /\*\*\*(.*?)\*\*\*/g, replacement: '<strong><em>$1</em></strong>' },
      { pattern: /\*\*(.*?)\*\*/g, replacement: '<strong>$1</strong>' },
      { pattern: /\*(.*?)\*/g, replacement: '<em>$1</em>' },
      { pattern: /\_\_\_(.*?)\_\_\_/g, replacement: '<strong><em>$1</em></strong>' },
      { pattern: /\_\_(.*?)\_\_/g, replacement: '<strong>$1</strong>' },
      { pattern: /\_(.*?)\_/g, replacement: '<em>$1</em>' },

      // 删除线
      { pattern: /~~(.*?)~~/g, replacement: '<del>$1</del>' },

      // 引用块（支持多行）
      { pattern: /^>\s*(.*$)/gm, replacement: '<blockquote-line>$1</blockquote-line>' },

      // 水平线
      { pattern: /^---+$/gm, replacement: '<hr>' },
      { pattern: /^\*\*\*+$/gm, replacement: '<hr>' },
      { pattern: /^___+$/gm, replacement: '<hr>' },
    ];
  }

  // 解析Markdown文本为HTML
  parse(markdown) {
    if (!markdown) return '';

    let html = markdown;

    // 预处理：标准化换行符和清理多余空白
    html = html
      .replace(/\r\n/g, '\n')  // 统一换行符
      .replace(/\r/g, '\n')    // 处理Mac格式
      .replace(/\n{3,}/g, '\n\n')  // 合并多个空行
      .trim();

    // 预处理规则
    this.preRules.forEach(rule => {
      html = html.replace(rule.pattern, rule.replacement);
    });

    // 处理表格
    html = this.processTables(html);

    // 应用主要规则
    this.rules.forEach(rule => {
      html = html.replace(rule.pattern, rule.replacement);
    });

    // 处理引用块
    html = this.processBlockquotes(html);

    // 处理列表
    html = this.processLists(html);

    // 处理段落
    html = this.processParagraphs(html);

    // 清理和后处理
    html = this.cleanup(html);

    return html;
  }

  // 处理表格
  processTables(html) {
    const lines = html.split('\n');
    const result = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i].trim();

      // 检查是否是表格行（包含 | 分隔符）
      if (line.includes('|') && line.length > 0) {
        const tableLines = [];
        let j = i;

        // 收集连续的表格行
        while (j < lines.length && lines[j].trim().includes('|')) {
          tableLines.push(lines[j].trim());
          j++;
        }

        if (tableLines.length >= 2) {
          // 检查第二行是否是分隔符行
          const separatorLine = tableLines[1];
          if (separatorLine.match(/^\|?[\s\-\|:]+\|?$/)) {
            result.push(this.buildTable(tableLines));
            i = j;
            continue;
          }
        }
      }

      result.push(lines[i]);
      i++;
    }

    return result.join('\n');
  }

  // 构建表格HTML
  buildTable(tableLines) {
    const headerLine = tableLines[0];
    const separatorLine = tableLines[1];
    const dataLines = tableLines.slice(2);

    // 解析表头
    const headers = this.parseTableRow(headerLine);

    // 解析对齐方式
    const alignments = this.parseTableAlignment(separatorLine);

    let html = '<table class="markdown-table">\n<thead>\n<tr>\n';
    headers.forEach((header, index) => {
      const align = alignments[index] || 'left';
      html += `<th style="text-align: ${align}">${header.trim()}</th>\n`;
    });
    html += '</tr>\n</thead>\n<tbody>\n';

    // 解析数据行
    dataLines.forEach(line => {
      const cells = this.parseTableRow(line);
      html += '<tr>\n';
      cells.forEach((cell, index) => {
        const align = alignments[index] || 'left';
        html += `<td style="text-align: ${align}">${cell.trim()}</td>\n`;
      });
      html += '</tr>\n';
    });

    html += '</tbody>\n</table>';
    return html;
  }

  // 解析表格行
  parseTableRow(line) {
    // 移除首尾的 | 符号
    line = line.replace(/^\||\|$/g, '');
    return line.split('|');
  }

  // 解析表格对齐方式
  parseTableAlignment(line) {
    const cells = this.parseTableRow(line);
    return cells.map(cell => {
      cell = cell.trim();
      if (cell.startsWith(':') && cell.endsWith(':')) {
        return 'center';
      } else if (cell.endsWith(':')) {
        return 'right';
      } else {
        return 'left';
      }
    });
  }

  // 处理引用块
  processBlockquotes(html) {
    // 将连续的引用行合并为一个引用块
    html = html.replace(/(<blockquote-line>.*?<\/blockquote-line>\s*)+/g, (match) => {
      const content = match.replace(/<\/?blockquote-line>/g, '').trim();
      return `<blockquote>${content}</blockquote>`;
    });

    return html;
  }

  // 处理列表
  processLists(html) {
    const lines = html.split('\n');
    const result = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i].trim();

      // 检查是否是列表项
      if (line.match(/^<li>/) || line.match(/^\s*[\*\-\+]\s+/) || line.match(/^\s*\d+\.\s+/)) {
        const listItems = [];
        let j = i;
        let isOrdered = false;

        // 收集连续的列表项
        while (j < lines.length) {
          const currentLine = lines[j].trim();
          if (currentLine.match(/^<li>/)) {
            listItems.push(currentLine);
          } else if (currentLine.match(/^\s*[\*\-\+]\s+(.+)/)) {
            const content = currentLine.replace(/^\s*[\*\-\+]\s+/, '');
            listItems.push(`<li>${content}</li>`);
          } else if (currentLine.match(/^\s*\d+\.\s+(.+)/)) {
            const content = currentLine.replace(/^\s*\d+\.\s+/, '');
            listItems.push(`<li>${content}</li>`);
            isOrdered = true;
          } else if (currentLine === '') {
            // 空行，继续检查下一行
          } else {
            break;
          }
          j++;
        }

        if (listItems.length > 0) {
          const tag = isOrdered ? 'ol' : 'ul';
          result.push(`<${tag}>${listItems.join('')}</${tag}>`);
          i = j;
          continue;
        }
      }

      result.push(lines[i]);
      i++;
    }

    return result.join('\n');
  }

  // 处理段落
  processParagraphs(html) {
    const lines = html.split('\n');
    const result = [];
    let currentParagraph = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // 检查是否是块级元素
      if (this.isBlockElement(line)) {
        // 如果有累积的段落内容，先处理它
        if (currentParagraph.length > 0) {
          const paragraphContent = currentParagraph.join(' ').trim();
          if (paragraphContent) {
            result.push(`<p>${paragraphContent}</p>`);
          }
          currentParagraph = [];
        }
        result.push(line);
      } else if (line === '') {
        // 空行，结束当前段落
        if (currentParagraph.length > 0) {
          const paragraphContent = currentParagraph.join(' ').trim();
          if (paragraphContent) {
            result.push(`<p>${paragraphContent}</p>`);
          }
          currentParagraph = [];
        }
      } else {
        // 普通文本行，添加到当前段落
        currentParagraph.push(line);
      }
    }

    // 处理最后的段落
    if (currentParagraph.length > 0) {
      const paragraphContent = currentParagraph.join(' ').trim();
      if (paragraphContent) {
        result.push(`<p>${paragraphContent}</p>`);
      }
    }

    return result.join('\n');
  }

  // 检查是否是块级元素
  isBlockElement(line) {
    const blockTags = ['<h[1-6]', '<ul>', '<ol>', '<li>', '<blockquote>', '<pre>', '<table>', '<hr>', '</ul>', '</ol>', '</blockquote>', '</pre>', '</table>'];
    return blockTags.some(tag => line.match(new RegExp(tag)));
  }

  // 清理HTML
  cleanup(html) {
    return html
      .replace(/&#92;([\\`*_{}[\]()#+\-.!])/g, '$1') // 恢复转义字符
      .replace(/<p><\/p>/g, '') // 移除空段落
      .replace(/\n\s*\n/g, '\n') // 合并多个换行
      .replace(/^\s+|\s+$/g, '') // 移除首尾空白
      .trim();
  }

  // 提取纯文本（用于字数统计等）
  extractText(markdown) {
    if (!markdown) return '';
    
    return markdown
      .replace(/```[\s\S]*?```/g, '') // 移除代码块
      .replace(/`[^`]+`/g, '') // 移除行内代码
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 保留链接文本
      .replace(/[#*>`\-_]/g, '') // 移除Markdown标记
      .replace(/\s+/g, ' ') // 合并空白字符
      .trim();
  }

  // 生成目录
  generateTOC(markdown) {
    if (!markdown) return '';
    
    const headings = [];
    const lines = markdown.split('\n');
    
    lines.forEach((line, index) => {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      if (match) {
        const level = match[1].length;
        const text = match[2].trim();
        const id = this.generateId(text);
        
        headings.push({
          level,
          text,
          id,
          line: index + 1
        });
      }
    });
    
    if (headings.length === 0) return '';
    
    let toc = '<div class="markdown-toc"><h4>目录</h4><ul>';
    
    headings.forEach(heading => {
      const indent = '  '.repeat(heading.level - 1);
      toc += `${indent}<li><a href="#${heading.id}">${heading.text}</a></li>`;
    });
    
    toc += '</ul></div>';
    
    return toc;
  }

  // 生成ID
  generateId(text) {
    return text
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fff\s-]/g, '') // 保留中文、英文、数字、空格、连字符
      .replace(/\s+/g, '-') // 空格替换为连字符
      .replace(/-+/g, '-') // 合并多个连字符
      .replace(/^-|-$/g, ''); // 移除首尾连字符
  }

  // 添加标题ID（用于目录跳转）
  addHeadingIds(html) {
    return html.replace(/<h([1-6])>(.*?)<\/h[1-6]>/g, (match, level, text) => {
      const id = this.generateId(text);
      return `<h${level} id="${id}">${text}</h${level}>`;
    });
  }

  // 完整解析（包含目录和标题ID）
  parseWithTOC(markdown) {
    const html = this.parse(markdown);
    const htmlWithIds = this.addHeadingIds(html);
    const toc = this.generateTOC(markdown);
    
    return {
      html: htmlWithIds,
      toc: toc,
      wordCount: this.extractText(markdown).split(/\s+/).length
    };
  }
}

// 导出解析器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MarkdownParser;
} else if (typeof window !== 'undefined') {
  window.MarkdownParser = MarkdownParser;
}
